16:38:03.212 debug: 2025/9/12 16:38:03
16:38:03.212 debug: Project: E:\M2Game\Client
16:38:03.212 debug: Targets: editor,preview
16:38:03.229 debug: Incremental file seems great.
16:38:03.230 debug: Engine path: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine
16:38:03.238 debug: Initializing target [Editor]
16:38:03.239 debug: Loading cache
16:38:03.244 debug: Loading cache costs 5.5733000000000175ms.
16:38:03.244 debug: Engine features shipped in editor: base,gfx-webgl,gfx-webgl2,gfx-empty,gfx-webgpu,3d,animation,skeletal-animation,2d,rich-text,mask,graphics,ui-skew,ui,affine-transform,particle,particle-2d,physics-framework,physics-cannon,physics-physx,physics-ammo,physics-builtin,physics-2d-framework,physics-2d-box2d-jsb,physics-2d-box2d,physics-2d-builtin,physics-2d-box2d-wasm,intersection-2d,primitive,profiler,occlusion-query,geometry-renderer,debug-renderer,audio,video,xr,light-probe,terrain,webview,tween,tiled-map,vendor-google,spine-3.8,spine-4.2,dragon-bones,marionette,procedural-animation,custom-pipeline,custom-pipeline-builtin-scripts,custom-pipeline-post-process,legacy-pipeline,websocket,websocket-server,meshopt
16:38:03.245 debug: Our import map(foo:/bar): {
  "imports": {
    "cc": "cce:/internal/x/cc"
  }
}
16:38:03.245 debug: Initializing target [Preview]
16:38:03.245 debug: Loading cache
16:38:03.251 debug: Loading cache costs 6.315100000000257ms.
16:38:03.251 debug: Our import map(foo:/bar): {
  "imports": {
    "cc": "cce:/internal/x/cc"
  }
}
16:38:03.283 debug: Sync engine features: 2d,affine-transform,animation,audio,base,custom-pipeline,gfx-webgl,gfx-webgl2,graphics,intersection-2d,marionette,mask,particle-2d,physics-2d-builtin,profiler,rich-text,skeletal-animation,spine-3.8,tween,ui,video,websocket,webview,custom-pipeline
16:38:03.285 debug: Reset databases. Enumerated domains: [
  {
    "root": "db://internal/",
    "physical": "C:\\ProgramData\\cocos\\editors\\Creator\\3.8.6\\resources\\resources\\3d\\engine\\editor\\assets"
  },
  {
    "root": "db://assets/",
    "physical": "E:\\M2Game\\Client\\assets"
  }
]
16:38:03.285 debug: Our import map(foo:/bar): {
  "imports": {
    "cc": "cce:/internal/x/cc",
    "db://internal/": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/",
    "db://assets/": "file:///E:/M2Game/Client/assets/"
  }
}
16:38:03.286 debug: Our import map(foo:/bar): {
  "imports": {
    "cc": "cce:/internal/x/cc",
    "db://internal/": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/",
    "db://assets/": "file:///E:/M2Game/Client/assets/"
  }
}
16:38:03.286 debug: Pulling asset-db.
16:38:03.322 debug: Fetch asset-db cost: 35.345600000000104ms.
16:38:03.322 debug: Build iteration starts.
Number of accumulated asset changes: 221
Feature changed: false
16:38:03.323 debug: Target(editor) build started.
16:38:03.325 debug: Detected change: cce:/internal/x/cc. Last mtime: Thu Jan 01 1970 08:00:03 GMT+0800 (中国标准时间), Current mtime: Thu Jan 01 1970 08:00:03 GMT+0800 (中国标准时间)
16:38:03.325 debug: Inspect cce:/internal/x/cc
16:38:03.359 debug: transform url: 'cce:/internal/x/cc' costs: 33.90 ms
16:38:03.360 debug: Resolve cce:/internal/x/cc-fu/base from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/base.
16:38:03.360 debug: Resolve cce:/internal/x/cc-fu/gfx-webgl from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-webgl.
16:38:03.360 debug: Resolve cce:/internal/x/cc-fu/gfx-webgl2 from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-webgl2.
16:38:03.360 debug: Resolve cce:/internal/x/cc-fu/gfx-empty from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-empty.
16:38:03.360 debug: Resolve cce:/internal/x/cc-fu/gfx-webgpu from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-webgpu.
16:38:03.360 debug: Resolve cce:/internal/x/cc-fu/3d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/3d.
16:38:03.360 debug: Resolve cce:/internal/x/cc-fu/animation from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/animation.
16:38:03.360 debug: Resolve cce:/internal/x/cc-fu/skeletal-animation from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/skeletal-animation.
16:38:03.360 debug: Resolve cce:/internal/x/cc-fu/2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/2d.
16:38:03.360 debug: Resolve cce:/internal/x/cc-fu/sorting from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/sorting.
16:38:03.360 debug: Resolve cce:/internal/x/cc-fu/rich-text from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/rich-text.
16:38:03.360 debug: Resolve cce:/internal/x/cc-fu/mask from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/mask.
16:38:03.360 debug: Resolve cce:/internal/x/cc-fu/graphics from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/graphics.
16:38:03.360 debug: Resolve cce:/internal/x/cc-fu/ui-skew from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/ui-skew.
16:38:03.360 debug: Resolve cce:/internal/x/cc-fu/ui from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/ui.
16:38:03.360 debug: Resolve cce:/internal/x/cc-fu/affine-transform from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/affine-transform.
16:38:03.360 debug: Resolve cce:/internal/x/cc-fu/particle from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/particle.
16:38:03.360 debug: Resolve cce:/internal/x/cc-fu/particle-2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/particle-2d.
16:38:03.360 debug: Resolve cce:/internal/x/cc-fu/physics-framework from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-framework.
16:38:03.360 debug: Resolve cce:/internal/x/cc-fu/physics-cannon from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-cannon.
16:38:03.360 debug: Resolve cce:/internal/x/cc-fu/physics-physx from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-physx.
16:38:03.360 debug: Resolve cce:/internal/x/cc-fu/physics-ammo from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-ammo.
16:38:03.360 debug: Resolve cce:/internal/x/cc-fu/physics-builtin from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-builtin.
16:38:03.360 debug: Resolve cce:/internal/x/cc-fu/physics-2d-framework from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-framework.
16:38:03.360 debug: Resolve cce:/internal/x/cc-fu/physics-2d-box2d-jsb from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-box2d-jsb.
16:38:03.360 debug: Resolve cce:/internal/x/cc-fu/physics-2d-box2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-box2d.
16:38:03.360 debug: Resolve cce:/internal/x/cc-fu/physics-2d-builtin from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-builtin.
16:38:03.360 debug: Resolve cce:/internal/x/cc-fu/physics-2d-box2d-wasm from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-box2d-wasm.
16:38:03.360 debug: Resolve cce:/internal/x/cc-fu/intersection-2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/intersection-2d.
16:38:03.360 debug: Resolve cce:/internal/x/cc-fu/primitive from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/primitive.
16:38:03.360 debug: Resolve cce:/internal/x/cc-fu/profiler from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/profiler.
16:38:03.360 debug: Resolve cce:/internal/x/cc-fu/geometry-renderer from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/geometry-renderer.
16:38:03.360 debug: Resolve cce:/internal/x/cc-fu/audio from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/audio.
16:38:03.360 debug: Resolve cce:/internal/x/cc-fu/video from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/video.
16:38:03.360 debug: Resolve cce:/internal/x/cc-fu/xr from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/xr.
16:38:03.360 debug: Resolve cce:/internal/x/cc-fu/light-probe from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/light-probe.
16:38:03.360 debug: Resolve cce:/internal/x/cc-fu/terrain from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/terrain.
16:38:03.360 debug: Resolve cce:/internal/x/cc-fu/webview from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/webview.
16:38:03.360 debug: Resolve cce:/internal/x/cc-fu/tween from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/tween.
16:38:03.361 debug: Resolve cce:/internal/x/cc-fu/tiled-map from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/tiled-map.
16:38:03.361 debug: Resolve cce:/internal/x/cc-fu/vendor-google from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/vendor-google.
16:38:03.361 debug: Resolve cce:/internal/x/cc-fu/spine from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/spine.
16:38:03.361 debug: Resolve cce:/internal/x/cc-fu/dragon-bones from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/dragon-bones.
16:38:03.361 debug: Resolve cce:/internal/x/cc-fu/custom-pipeline from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/custom-pipeline.
16:38:03.361 debug: Resolve cce:/internal/x/cc-fu/custom-pipeline-post-process from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/custom-pipeline-post-process.
16:38:03.361 debug: Resolve cce:/internal/x/cc-fu/legacy-pipeline from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/legacy-pipeline.
16:38:03.361 debug: Detected change: cce:/internal/x/prerequisite-imports. Last mtime: Thu Jan 01 1970 08:00:09 GMT+0800 (中国标准时间), Current mtime: Thu Jan 01 1970 08:00:03 GMT+0800 (中国标准时间)
16:38:03.361 debug: Inspect cce:/internal/x/prerequisite-imports
16:38:03.395 debug: transform url: 'cce:/internal/x/prerequisite-imports' costs: 34.60 ms
16:38:03.397 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts.
16:38:03.398 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts.
16:38:03.399 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
16:38:03.399 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
16:38:03.400 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts.
16:38:03.400 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts.
16:38:03.401 debug: Resolve file:///E:/M2Game/Client/assets/bundles/Bundle.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/Bundle.ts.
16:38:03.401 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts.
16:38:03.402 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/const/AttributeConst.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/const/AttributeConst.ts.
16:38:03.402 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/data/DataManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/data/DataManager.ts.
16:38:03.403 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/data/bag/Bag.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/data/bag/Bag.ts.
16:38:03.403 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/data/base/AttributeData.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/data/base/AttributeData.ts.
16:38:03.404 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/data/base/BaseInfo.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/data/base/BaseInfo.ts.
16:38:03.404 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/data/equip/Equip.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/data/equip/Equip.ts.
16:38:03.404 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/data/equip/EquipCombine.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/data/equip/EquipCombine.ts.
16:38:03.405 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/data/equip/EquipSlots.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/data/equip/EquipSlots.ts.
16:38:03.405 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/data/fight/Rogue.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/data/fight/Rogue.ts.
16:38:03.405 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/data/game_level/GameLevel.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/data/game_level/GameLevel.ts.
16:38:03.406 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/data/gm/GM.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/data/gm/GM.ts.
16:38:03.407 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/data/mail/Mail.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/data/mail/Mail.ts.
16:38:03.407 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/data/pk/PK.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/data/pk/PK.ts.
16:38:03.407 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/data/plane/PlaneCacheInfo.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/data/plane/PlaneCacheInfo.ts.
16:38:03.408 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/data/plane/PlaneData.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/data/plane/PlaneData.ts.
16:38:03.408 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/event/DataEvent.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/event/DataEvent.ts.
16:38:03.409 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/event/EventManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/event/EventManager.ts.
16:38:03.409 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/event/HomeUIEvent.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/event/HomeUIEvent.ts.
16:38:03.410 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/event/PlaneUIEvent.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/event/PlaneUIEvent.ts.
16:38:03.410 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/Plane.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/Plane.ts.
16:38:03.411 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/TopBlockInputUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/TopBlockInputUI.ts.
16:38:03.411 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/SelectList/uiSelect.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/SelectList/uiSelect.ts.
16:38:03.412 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/SelectList/uiSelectItem.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/SelectList/uiSelectItem.ts.
16:38:03.413 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/button/ButtonPlus.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/button/ButtonPlus.ts.
16:38:03.413 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/button/DragButton.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/button/DragButton.ts.
16:38:03.413 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/dropdown/DropDown.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/dropdown/DropDown.ts.
16:38:03.414 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/list/List.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/list/List.ts.
16:38:03.414 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/list/ListItem.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/list/ListItem.ts.
16:38:03.415 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/friend/FriendAddUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/friend/FriendAddUI.ts.
16:38:03.415 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/friend/FriendCellUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/friend/FriendCellUI.ts.
16:38:03.415 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/friend/FriendListUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/friend/FriendListUI.ts.
16:38:03.416 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/friend/FriendStrangerUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/friend/FriendStrangerUI.ts.
16:38:03.416 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/friend/FriendUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/friend/FriendUI.ts.
16:38:03.416 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>
16:38:03.416 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>
16:38:03.417 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>
16:38:03.417 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>
16:38:03.417 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>
16:38:03.418 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>
16:38:03.418 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>
16:38:03.419 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>
16:38:03.419 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/DialogueUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/DialogueUI.ts.
16:38:03.419 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/RogueSelectIcon.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/RogueSelectIcon.ts.
16:38:03.420 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/RogueUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/RogueUI.ts.
16:38:03.420 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/mail/MailCellUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/mail/MailCellUI.ts.
16:38:03.421 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/mail/MailUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/mail/MailUI.ts.
16:38:03.421 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/pk/PKHistoryCellUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/pk/PKHistoryCellUI.ts.
16:38:03.421 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/pk/PKHistoryUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/pk/PKHistoryUI.ts.
16:38:03.422 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/pk/PKRewardIcon.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/pk/PKRewardIcon.ts.
16:38:03.422 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/pk/PKUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/pk/PKUI.ts.
16:38:03.423 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/PlaneCombineResultUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/PlaneCombineResultUI.ts.
16:38:03.423 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/PlaneEquipInfoUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/PlaneEquipInfoUI.ts.
16:38:03.424 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/PlaneTypes.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/PlaneTypes.ts.
16:38:03.424 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/PlaneUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/PlaneUI.ts.
16:38:03.424 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/components/back_pack/BagGrid.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/components/back_pack/BagGrid.ts.
16:38:03.425 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/components/back_pack/BagItem.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/components/back_pack/BagItem.ts.
16:38:03.425 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/components/back_pack/SortTypeDropdown.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/components/back_pack/SortTypeDropdown.ts.
16:38:03.426 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/components/back_pack/Tabs.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/components/back_pack/Tabs.ts.
16:38:03.426 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/components/display/CombineDisplay.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/components/display/CombineDisplay.ts.
16:38:03.426 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/components/display/EquipDisplay.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/components/display/EquipDisplay.ts.
16:38:03.427 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/shop/ShopUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/shop/ShopUI.ts.
16:38:03.427 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/skyisland/SkyIslandUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/skyisland/SkyIslandUI.ts.
16:38:03.427 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/story/BuidingUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/story/BuidingUI.ts.
16:38:03.427 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/story/BuildingInfoUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/story/BuildingInfoUI.ts.
16:38:03.428 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/story/StoryUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/story/StoryUI.ts.
16:38:03.428 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/talent/TalentUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/talent/TalentUI.ts.
16:38:03.428 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/task/TaskUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/task/TaskUI.ts.
16:38:03.429 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/task/components/ProgressPanel.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/task/components/ProgressPanel.ts.
16:38:03.429 debug: Resolve file:///E:/M2Game/Client/assets/bundles/gm/script/GmEntry.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/gm/script/GmEntry.ts.
16:38:03.429 debug: Resolve file:///E:/M2Game/Client/assets/bundles/gm/script/ui/GmButtonUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/gm/script/ui/GmButtonUI.ts.
16:38:03.429 debug: Resolve file:///E:/M2Game/Client/assets/bundles/gm/script/ui/GmUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/gm/script/ui/GmUI.ts.
16:38:03.430 debug: Resolve file:///E:/M2Game/Client/assets/editor/emitter/EmitterEditor.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/editor/emitter/EmitterEditor.ts.
16:38:03.430 debug: Resolve file:///E:/M2Game/Client/assets/editor/gizmos/EmitterGizmo.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/editor/gizmos/EmitterGizmo.ts.
16:38:03.430 debug: Resolve file:///E:/M2Game/Client/assets/editor/gizmos/GizmoDrawer.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/editor/gizmos/GizmoDrawer.ts.
16:38:03.430 debug: Resolve file:///E:/M2Game/Client/assets/editor/gizmos/GizmoManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/editor/gizmos/GizmoManager.ts.
16:38:03.431 debug: Resolve file:///E:/M2Game/Client/assets/editor/gizmos/GizmoUtils.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/editor/gizmos/GizmoUtils.ts.
16:38:03.431 debug: Resolve file:///E:/M2Game/Client/assets/editor/level/LevelEditorBaseUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/editor/level/LevelEditorBaseUI.ts.
16:38:03.431 debug: Resolve file:///E:/M2Game/Client/assets/editor/level/LevelEditorCondition.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/editor/level/LevelEditorCondition.ts.
16:38:03.431 debug: Resolve file:///E:/M2Game/Client/assets/editor/level/LevelEditorElemUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/editor/level/LevelEditorElemUI.ts.
16:38:03.432 debug: Resolve file:///E:/M2Game/Client/assets/editor/level/LevelEditorEventUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/editor/level/LevelEditorEventUI.ts.
16:38:03.432 debug: Resolve file:///E:/M2Game/Client/assets/editor/level/LevelEditorLayerUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/editor/level/LevelEditorLayerUI.ts.
16:38:03.432 debug: Resolve file:///E:/M2Game/Client/assets/editor/level/LevelEditorUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/editor/level/LevelEditorUI.ts.
16:38:03.432 debug: Resolve file:///E:/M2Game/Client/assets/editor/level/LevelEditorWaveParam.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/editor/level/LevelEditorWaveParam.ts.
16:38:03.432 debug: Resolve file:///E:/M2Game/Client/assets/editor/level/LevelEditorWaveUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/editor/level/LevelEditorWaveUI.ts.
16:38:03.433 debug: Resolve file:///E:/M2Game/Client/assets/editor/level/utils.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/editor/level/utils.ts.
16:38:03.433 debug: Resolve file:///E:/M2Game/Client/assets/editor/planeview/PlaneView.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/editor/planeview/PlaneView.ts.
16:38:03.433 debug: Resolve file:///E:/M2Game/Client/assets/scripts/ColliderTest.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/ColliderTest.ts.
16:38:03.433 debug: Resolve file:///E:/M2Game/Client/assets/scripts/IMgr.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/IMgr.ts.
16:38:03.434 debug: Resolve file:///E:/M2Game/Client/assets/scripts/MainUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/MainUI.ts.
16:38:03.434 debug: Resolve file:///E:/M2Game/Client/assets/scripts/MyApp.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/MyApp.ts.
16:38:03.434 debug: Resolve file:///E:/M2Game/Client/assets/scripts/autogen/luban/schema.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/autogen/luban/schema.ts.
16:38:03.435 debug: Resolve file:///E:/M2Game/Client/assets/scripts/core/base/PlaneManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/core/base/PlaneManager.ts.
16:38:03.435 debug: Resolve file:///E:/M2Game/Client/assets/scripts/core/base/ResManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/core/base/ResManager.ts.
16:38:03.435 debug: Resolve file:///E:/M2Game/Client/assets/scripts/core/base/SingletonBase.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/core/base/SingletonBase.ts.
16:38:03.436 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/GameFunc.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/GameFunc.ts.
16:38:03.436 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/GameIns.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/GameIns.ts.
16:38:03.437 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/bullet/Bullet.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/bullet/Bullet.ts.
16:38:03.437 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/bullet/BulletSystem.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/bullet/BulletSystem.ts.
16:38:03.438 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/bullet/Easing.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/bullet/Easing.ts.
16:38:03.438 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/bullet/Emitter.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/bullet/Emitter.ts.
16:38:03.438 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/bullet/EventGroup.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/bullet/EventGroup.ts.
16:38:03.438 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/bullet/EventRunner.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/bullet/EventRunner.ts.
16:38:03.438 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/bullet/ObjectPool.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/bullet/ObjectPool.ts.
16:38:03.439 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/bullet/PropertyContainer.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/bullet/PropertyContainer.ts.
16:38:03.439 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/bullet/actions/BulletEventActions.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/bullet/actions/BulletEventActions.ts.
16:38:03.439 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/bullet/actions/EmitterEventActions.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/bullet/actions/EmitterEventActions.ts.
16:38:03.440 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/bullet/actions/IEventAction.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/bullet/actions/IEventAction.ts.
16:38:03.440 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/bullet/conditions/BulletEventConditions.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/bullet/conditions/BulletEventConditions.ts.
16:38:03.440 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/bullet/conditions/EmitterEventConditions.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/bullet/conditions/EmitterEventConditions.ts.
16:38:03.441 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/bullet/conditions/IEventCondition.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/bullet/conditions/IEventCondition.ts.
16:38:03.441 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/collider-system/FBoxCollider.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/collider-system/FBoxCollider.ts.
16:38:03.441 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/collider-system/FCircleCollider.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/collider-system/FCircleCollider.ts.
16:38:03.441 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/collider-system/FCollider.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/collider-system/FCollider.ts.
16:38:03.442 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/collider-system/FColliderManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/collider-system/FColliderManager.ts.
16:38:03.442 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/collider-system/FPolygonCollider.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/collider-system/FPolygonCollider.ts.
16:38:03.443 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/collider-system/Intersection.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/collider-system/Intersection.ts.
16:38:03.443 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/collider-system/QuadTree.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/collider-system/QuadTree.ts.
16:38:03.443 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/const/GameConst.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/const/GameConst.ts.
16:38:03.443 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/const/GameEnum.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/const/GameEnum.ts.
16:38:03.444 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/const/GameResourceList.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/const/GameResourceList.ts.
16:38:03.444 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/data/BossData.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/data/BossData.ts.
16:38:03.444 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/data/BulletEventData.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/data/BulletEventData.ts.
16:38:03.444 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/data/EnemyData.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/data/EnemyData.ts.
16:38:03.445 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/data/EnemyWave.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/data/EnemyWave.ts.
16:38:03.453 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/data/GameMapData.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/data/GameMapData.ts.
16:38:03.453 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/data/MainPlaneFightData.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/data/MainPlaneFightData.ts.
16:38:03.453 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/data/MapItemData.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/data/MapItemData.ts.
16:38:03.454 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/data/StageData.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/data/StageData.ts.
16:38:03.454 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/data/TrackData.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/data/TrackData.ts.
16:38:03.454 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/data/WaveData.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/data/WaveData.ts.
16:38:03.455 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/data/bullet/BulletData.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/data/bullet/BulletData.ts.
16:38:03.455 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/data/bullet/EmitterData.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/data/bullet/EmitterData.ts.
16:38:03.455 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/data/bullet/EventActionType.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/data/bullet/EventActionType.ts.
16:38:03.455 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/data/bullet/EventConditionType.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/data/bullet/EventConditionType.ts.
16:38:03.455 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/data/bullet/EventGroupData.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/data/bullet/EventGroupData.ts.
16:38:03.456 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/data/bullet/ExpressionValue.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/data/bullet/ExpressionValue.ts.
16:38:03.456 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/level/LevelItem.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/level/LevelItem.ts.
16:38:03.457 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/level/LevelItemEvent.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/level/LevelItemEvent.ts.
16:38:03.457 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/manager/BattleManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/manager/BattleManager.ts.
16:38:03.458 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/manager/BossManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/manager/BossManager.ts.
16:38:03.458 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/manager/EnemyManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/manager/EnemyManager.ts.
16:38:03.458 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/manager/GameDataManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/manager/GameDataManager.ts.
16:38:03.458 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/manager/GamePlaneManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/manager/GamePlaneManager.ts.
16:38:03.458 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/manager/GameResManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/manager/GameResManager.ts.
16:38:03.459 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/manager/GameRuleManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/manager/GameRuleManager.ts.
16:38:03.459 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/manager/GlobalDataManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/manager/GlobalDataManager.ts.
16:38:03.459 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/manager/HurtEffectManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/manager/HurtEffectManager.ts.
16:38:03.459 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/manager/MainPlaneManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/manager/MainPlaneManager.ts.
16:38:03.459 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/manager/SceneManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/manager/SceneManager.ts.
16:38:03.460 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/manager/StageManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/manager/StageManager.ts.
16:38:03.460 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/manager/WaveManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/manager/WaveManager.ts.
16:38:03.460 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/move/IMovable.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/move/IMovable.ts.
16:38:03.460 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/move/Movable.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/move/Movable.ts.
16:38:03.460 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/randTerrain/RandTerrain.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/randTerrain/RandTerrain.ts.
16:38:03.461 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/scenes/GameMain.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/scenes/GameMain.ts.
16:38:03.461 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/ui/base/BaseComp.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/ui/base/BaseComp.ts.
16:38:03.461 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/ui/base/Controller.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/ui/base/Controller.ts.
16:38:03.462 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/ui/base/Entity.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/ui/base/Entity.ts.
16:38:03.462 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/ui/base/ImageSequence.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/ui/base/ImageSequence.ts.
16:38:03.462 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/ui/base/NodeMove.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/ui/base/NodeMove.ts.
16:38:03.463 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/ui/base/PfFrameAnim.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/ui/base/PfFrameAnim.ts.
16:38:03.463 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/ui/base/TrackComponent.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/ui/base/TrackComponent.ts.
16:38:03.463 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/ui/base/UIAnimMethods.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/ui/base/UIAnimMethods.ts.
16:38:03.463 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/ui/layer/BattleLayer.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/ui/layer/BattleLayer.ts.
16:38:03.463 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/ui/layer/EffectLayer.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/ui/layer/EffectLayer.ts.
16:38:03.464 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/ui/layer/EnemyEffectLayer.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/ui/layer/EnemyEffectLayer.ts.
16:38:03.464 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/ui/map/GameMapRun.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/ui/map/GameMapRun.ts.
16:38:03.464 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/ui/map/LevelBaseUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/ui/map/LevelBaseUI.ts.
16:38:03.464 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/ui/map/LevelCondition.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/ui/map/LevelCondition.ts.
16:38:03.465 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/ui/map/LevelElemUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/ui/map/LevelElemUI.ts.
16:38:03.465 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/ui/map/LevelEventUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/ui/map/LevelEventUI.ts.
16:38:03.465 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/ui/map/LevelLayerUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/ui/map/LevelLayerUI.ts.
16:38:03.466 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/ui/map/LevelWaveUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/ui/map/LevelWaveUI.ts.
16:38:03.466 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/ui/plane/PlaneBase.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/ui/plane/PlaneBase.ts.
16:38:03.466 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/ui/plane/boss/BossPlane.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/ui/plane/boss/BossPlane.ts.
16:38:03.467 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/ui/plane/enemy/EnemyPlane.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/ui/plane/enemy/EnemyPlane.ts.
16:38:03.467 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/ui/plane/enemy/EnemyPlaneBase.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/ui/plane/enemy/EnemyPlaneBase.ts.
16:38:03.467 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/ui/plane/mainPlane/MainPlane.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/ui/plane/mainPlane/MainPlane.ts.
16:38:03.467 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/ui/plane/skill/BuffComp.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/ui/plane/skill/BuffComp.ts.
16:38:05.722 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/ui/plane/skill/SkillComp.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/ui/plane/skill/SkillComp.ts.
16:38:05.722 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/ui/plane/weapon/Weapon.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/ui/plane/weapon/Weapon.ts.
16:38:05.722 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/utils/Helper.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/utils/Helper.ts.
16:38:05.722 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/utils/RPN.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/utils/RPN.ts.
16:38:05.722 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/utils/Tools.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/utils/Tools.ts.
16:38:05.722 debug: Resolve file:///E:/M2Game/Client/assets/scripts/game/wave/Wave.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/game/wave/Wave.ts.
16:38:05.722 debug: Resolve file:///E:/M2Game/Client/assets/scripts/leveldata/condition/LevelDataEventCondtion.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/leveldata/condition/LevelDataEventCondtion.ts.
16:38:05.722 debug: Resolve file:///E:/M2Game/Client/assets/scripts/leveldata/condition/LevelDataEventCondtionDelayDistance.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/leveldata/condition/LevelDataEventCondtionDelayDistance.ts.
16:38:05.722 debug: Resolve file:///E:/M2Game/Client/assets/scripts/leveldata/condition/LevelDataEventCondtionDelayTime.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/leveldata/condition/LevelDataEventCondtionDelayTime.ts.
16:38:05.722 debug: Resolve file:///E:/M2Game/Client/assets/scripts/leveldata/condition/LevelDataEventCondtionWave.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/leveldata/condition/LevelDataEventCondtionWave.ts.
16:38:05.722 debug: Resolve file:///E:/M2Game/Client/assets/scripts/leveldata/condition/newCondition.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/leveldata/condition/newCondition.ts.
16:38:05.722 debug: Resolve file:///E:/M2Game/Client/assets/scripts/leveldata/leveldata.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/leveldata/leveldata.ts.
16:38:05.722 debug: Resolve file:///E:/M2Game/Client/assets/scripts/leveldata/trigger/LevelDataEventTrigger.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/leveldata/trigger/LevelDataEventTrigger.ts.
16:38:05.722 debug: Resolve file:///E:/M2Game/Client/assets/scripts/leveldata/trigger/LevelDataEventTriggerAudio.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/leveldata/trigger/LevelDataEventTriggerAudio.ts.
16:38:05.722 debug: Resolve file:///E:/M2Game/Client/assets/scripts/leveldata/trigger/LevelDataEventTriggerLog.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/leveldata/trigger/LevelDataEventTriggerLog.ts.
16:38:05.722 debug: Resolve file:///E:/M2Game/Client/assets/scripts/leveldata/trigger/LevelDataEventTriggerWave.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/leveldata/trigger/LevelDataEventTriggerWave.ts.
16:38:05.722 debug: Resolve file:///E:/M2Game/Client/assets/scripts/leveldata/trigger/newTrigger.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/leveldata/trigger/newTrigger.ts.
16:38:05.722 debug: Resolve file:///E:/M2Game/Client/assets/scripts/luban/LubanMgr.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/luban/LubanMgr.ts.
16:38:05.722 debug: Resolve file:///E:/M2Game/Client/assets/scripts/network/NetMgr.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/network/NetMgr.ts.
16:38:05.722 debug: Resolve file:///E:/M2Game/Client/assets/scripts/platformsdk/DevLogin.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/platformsdk/DevLogin.ts.
16:38:05.722 debug: Resolve file:///E:/M2Game/Client/assets/scripts/platformsdk/DevLoginData.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/platformsdk/DevLoginData.ts.
16:38:05.722 debug: Resolve file:///E:/M2Game/Client/assets/scripts/platformsdk/IPlatformSDK.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/platformsdk/IPlatformSDK.ts.
16:38:05.722 debug: Resolve file:///E:/M2Game/Client/assets/scripts/platformsdk/WXLogin.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/platformsdk/WXLogin.ts.
16:38:05.722 debug: Resolve file:///E:/M2Game/Client/assets/scripts/resupdate/ResUpdate.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/resupdate/ResUpdate.ts.
16:38:05.722 debug: Resolve file:///E:/M2Game/Client/assets/scripts/resupdate/RootPersist.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/resupdate/RootPersist.ts.
16:38:05.722 debug: Resolve file:///E:/M2Game/Client/assets/scripts/resupdate/audioManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/resupdate/audioManager.ts.
16:38:05.722 debug: Resolve file:///E:/M2Game/Client/assets/scripts/ui/DevLoginUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/ui/DevLoginUI.ts.
16:38:05.722 debug: Resolve file:///E:/M2Game/Client/assets/scripts/ui/LoadingUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/ui/LoadingUI.ts.
16:38:05.722 debug: Resolve file:///E:/M2Game/Client/assets/scripts/ui/RatioScaler.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/ui/RatioScaler.ts.
16:38:05.722 debug: Resolve file:///E:/M2Game/Client/assets/scripts/ui/UIMgr.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/ui/UIMgr.ts.
16:38:05.722 debug: Resolve file:///E:/M2Game/Client/assets/scripts/ui/game/MBoomUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/ui/game/MBoomUI.ts.
16:38:05.722 debug: Resolve file:///E:/M2Game/Client/assets/scripts/ui/res/PlaneRes.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/ui/res/PlaneRes.ts.
16:38:05.722 debug: Resolve file:///E:/M2Game/Client/assets/scripts/utils/Logger.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/utils/Logger.ts.
16:38:05.722 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/code-quality/cr.mjs.
16:38:05.722 debug: Detected change: cce:/internal/code-quality/cr.mjs. Last mtime: Fri Sep 12 2025 16:32:18 GMT+0800 (中国标准时间), Current mtime: Fri Sep 12 2025 16:37:59 GMT+0800 (中国标准时间)
16:38:05.722 debug: Inspect cce:/internal/code-quality/cr.mjs
16:38:05.722 debug: transform url: 'cce:/internal/code-quality/cr.mjs' costs: 8.90 ms
16:38:05.722 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
16:38:05.722 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
16:38:05.722 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
16:38:05.722 debug: Resolve cc/env from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as external dependency cc/env.
16:38:05.722 debug: Resolve ./builtin-pipeline-settings from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
16:38:05.722 debug: Resolve ./builtin-pipeline-pass from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts.
16:38:05.722 debug: Resolve ./builtin-pipeline from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts.
16:38:05.722 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/code-quality/cr.mjs.
16:38:05.722 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
16:38:05.722 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
16:38:05.722 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
16:38:05.722 debug: Resolve cc/env from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as external dependency cc/env.
16:38:05.722 debug: Resolve ./builtin-pipeline-types from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
16:38:05.722 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
16:38:05.722 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
16:38:05.722 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
16:38:05.722 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/code-quality/cr.mjs.
16:38:05.722 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
16:38:05.722 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
16:38:05.722 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
16:38:05.722 debug: Resolve ./builtin-pipeline-settings from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
16:38:05.722 debug: Resolve cc/env from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as external dependency cc/env.
16:38:05.722 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/code-quality/cr.mjs.
16:38:05.722 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
16:38:05.722 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
16:38:05.722 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
16:38:05.722 debug: Resolve cc/env from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as external dependency cc/env.
16:38:05.722 debug: Resolve ./builtin-pipeline-types from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
16:38:05.722 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
16:38:05.722 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
16:38:05.722 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
16:38:05.722 debug: Resolve cc from file:///E:/M2Game/Client/assets/bundles/Bundle.ts as cce:/internal/x/cc.
16:38:05.722 debug: Resolve cc from file:///E:/M2Game/Client/assets/bundles/Bundle.ts as cce:/internal/x/cc.
16:38:05.722 debug: Resolve cc from file:///E:/M2Game/Client/assets/bundles/Bundle.ts as cce:/internal/x/cc.
16:38:05.722 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as cce:/internal/code-quality/cr.mjs.
16:38:05.722 debug: Resolve cc from file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as cce:/internal/x/cc.
16:38:05.722 debug: Resolve cc from file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as cce:/internal/x/cc.
16:38:05.722 debug: Resolve cc from file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as cce:/internal/x/cc.
16:38:05.722 debug: Resolve db://assets/bundles/Bundle from file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as file:///E:/M2Game/Client/assets/bundles/Bundle.ts.
16:38:05.723 debug: Resolve db://assets/scripts/MyApp from file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as file:///E:/M2Game/Client/assets/scripts/MyApp.ts.
16:38:05.723 debug: Resolve db://assets/scripts/ui/UIMgr from file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as file:///E:/M2Game/Client/assets/scripts/ui/UIMgr.ts.
16:38:05.723 debug: Resolve db://assets/scripts/utils/Logger from file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as file:///E:/M2Game/Client/assets/scripts/utils/Logger.ts.
16:38:05.724 debug: Resolve ./event/EventManager from file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as file:///E:/M2Game/Client/assets/bundles/common/script/event/EventManager.ts.
16:38:05.724 debug: Resolve ./event/HomeUIEvent from file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as file:///E:/M2Game/Client/assets/bundles/common/script/event/HomeUIEvent.ts.
16:38:05.724 debug: Resolve ./ui/home/<USER>///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>
16:38:05.724 debug: Resolve ./ui/home/<USER>///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>
16:38:05.724 debug: Resolve ./ui/home/<USER>///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>
16:38:05.724 debug: Resolve ./ui/home/<USER>///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>
16:38:05.724 debug: Resolve ./ui/plane/PlaneUI from file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/PlaneUI.ts.
16:38:05.724 debug: Resolve ./ui/shop/ShopUI from file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as file:///E:/M2Game/Client/assets/bundles/common/script/ui/shop/ShopUI.ts.
16:38:05.724 debug: Resolve ./ui/skyisland/SkyIslandUI from file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as file:///E:/M2Game/Client/assets/bundles/common/script/ui/skyisland/SkyIslandUI.ts.
16:38:05.724 debug: Resolve ./ui/talent/TalentUI from file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as file:///E:/M2Game/Client/assets/bundles/common/script/ui/talent/TalentUI.ts.
16:38:05.724 debug: Resolve ./ui/story/StoryUI from file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as file:///E:/M2Game/Client/assets/bundles/common/script/ui/story/StoryUI.ts.
16:38:05.724 debug: Resolve ./ui/friend/FriendUI from file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as file:///E:/M2Game/Client/assets/bundles/common/script/ui/friend/FriendUI.ts.
16:38:05.724 debug: Resolve ./ui/mail/MailUI from file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as file:///E:/M2Game/Client/assets/bundles/common/script/ui/mail/MailUI.ts.
16:38:05.724 debug: Resolve ./ui/pk/PKUI from file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as file:///E:/M2Game/Client/assets/bundles/common/script/ui/pk/PKUI.ts.
16:38:05.724 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/MyApp.ts as cce:/internal/code-quality/cr.mjs.
16:38:05.724 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/MyApp.ts as cce:/internal/x/cc.
16:38:05.724 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/MyApp.ts as cce:/internal/x/cc.
16:38:05.724 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/MyApp.ts as cce:/internal/x/cc.
16:38:05.724 debug: Resolve ./luban/LubanMgr from file:///E:/M2Game/Client/assets/scripts/MyApp.ts as file:///E:/M2Game/Client/assets/scripts/luban/LubanMgr.ts.
16:38:05.724 debug: Resolve ./network/NetMgr from file:///E:/M2Game/Client/assets/scripts/MyApp.ts as file:///E:/M2Game/Client/assets/scripts/network/NetMgr.ts.
16:38:05.724 debug: Resolve ./resupdate/audioManager from file:///E:/M2Game/Client/assets/scripts/MyApp.ts as file:///E:/M2Game/Client/assets/scripts/resupdate/audioManager.ts.
16:38:05.724 debug: Resolve ./platformsdk/IPlatformSDK from file:///E:/M2Game/Client/assets/scripts/MyApp.ts as file:///E:/M2Game/Client/assets/scripts/platformsdk/IPlatformSDK.ts.
16:38:05.724 debug: Resolve ./core/base/ResManager from file:///E:/M2Game/Client/assets/scripts/MyApp.ts as file:///E:/M2Game/Client/assets/scripts/core/base/ResManager.ts.
16:38:05.724 debug: Resolve ./game/manager/GlobalDataManager from file:///E:/M2Game/Client/assets/scripts/MyApp.ts as file:///E:/M2Game/Client/assets/scripts/game/manager/GlobalDataManager.ts.
16:38:05.724 debug: Resolve ./core/base/PlaneManager from file:///E:/M2Game/Client/assets/scripts/MyApp.ts as file:///E:/M2Game/Client/assets/scripts/core/base/PlaneManager.ts.
16:38:05.724 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/luban/LubanMgr.ts as cce:/internal/code-quality/cr.mjs.
16:38:05.724 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/luban/LubanMgr.ts as cce:/internal/x/cc.
16:38:05.724 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/luban/LubanMgr.ts as cce:/internal/x/cc.
16:38:05.724 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/luban/LubanMgr.ts as cce:/internal/x/cc.
16:38:05.724 debug: Resolve ../IMgr from file:///E:/M2Game/Client/assets/scripts/luban/LubanMgr.ts as file:///E:/M2Game/Client/assets/scripts/IMgr.ts.
16:38:05.724 debug: Resolve ../autogen/luban/schema from file:///E:/M2Game/Client/assets/scripts/luban/LubanMgr.ts as file:///E:/M2Game/Client/assets/scripts/autogen/luban/schema.ts.
16:38:05.724 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/IMgr.ts as cce:/internal/x/cc.
16:38:05.724 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/IMgr.ts as cce:/internal/x/cc.
16:38:05.724 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/IMgr.ts as cce:/internal/x/cc.
16:38:05.724 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/autogen/luban/schema.ts as cce:/internal/x/cc.
16:38:05.724 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/network/NetMgr.ts as cce:/internal/code-quality/cr.mjs.
16:38:05.724 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/network/NetMgr.ts as cce:/internal/x/cc.
16:38:05.724 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/network/NetMgr.ts as cce:/internal/x/cc.
16:38:05.724 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/network/NetMgr.ts as cce:/internal/x/cc.
16:38:05.724 debug: Resolve long from file:///E:/M2Game/Client/assets/scripts/network/NetMgr.ts as file:///E:/M2Game/Client/node_modules/long/index.js.
16:38:05.724 debug: Create cjs interop url for 'file:///E:/M2Game/Client/assets/scripts/autogen/pb/cs_proto.js'
16:38:05.724 debug: Resolve ../autogen/pb/cs_proto.js from file:///E:/M2Game/Client/assets/scripts/network/NetMgr.ts as file:///E:/M2Game/Client/assets/scripts/autogen/pb/cs_proto.mjs?cjs=&original=.js.
16:38:05.724 debug: Resolve ../IMgr from file:///E:/M2Game/Client/assets/scripts/network/NetMgr.ts as file:///E:/M2Game/Client/assets/scripts/IMgr.ts.
16:38:05.724 debug: Resolve ../utils/Logger from file:///E:/M2Game/Client/assets/scripts/network/NetMgr.ts as file:///E:/M2Game/Client/assets/scripts/utils/Logger.ts.
16:38:05.724 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/autogen/pb/cs_proto.mjs?cjs=&original=.js as cce:/internal/code-quality/cr.mjs.
16:38:05.725 debug: Resolve ./cs_proto.js from file:///E:/M2Game/Client/assets/scripts/autogen/pb/cs_proto.mjs?cjs=&original=.js as file:///E:/M2Game/Client/assets/scripts/autogen/pb/cs_proto.js.
16:38:05.725 debug: Detected change: file:///E:/M2Game/Client/assets/scripts/autogen/pb/cs_proto.js. Last mtime: Fri Sep 12 2025 09:16:33 GMT+0800 (中国标准时间)@3c91fcd0-2c60-4096-a1f6-a93ea5a2458d, Current mtime: Fri Sep 12 2025 09:16:33 GMT+0800 (中国标准时间)
16:38:05.725 debug: Inspect file:///E:/M2Game/Client/assets/scripts/autogen/pb/cs_proto.js
16:38:05.725 debug: transform url: 'file:///E:/M2Game/Client/assets/scripts/autogen/pb/cs_proto.js' costs: 1976.60 ms
16:38:05.725 debug: Resolve cce:/internal/ml/cjs-loader.mjs from file:///E:/M2Game/Client/assets/scripts/autogen/pb/cs_proto.mjs?cjs=&original=.js as cce:/internal/ml/cjs-loader.mjs.
16:38:05.725 debug: Detected change: cce:/internal/ml/cjs-loader.mjs. Last mtime: Fri Sep 12 2025 16:32:18 GMT+0800 (中国标准时间), Current mtime: Fri Sep 12 2025 16:37:59 GMT+0800 (中国标准时间)
16:38:05.725 debug: Inspect cce:/internal/ml/cjs-loader.mjs
16:38:05.725 debug: transform url: 'cce:/internal/ml/cjs-loader.mjs' costs: 15.20 ms
16:38:05.725 debug: Resolve ./cs_proto.js from file:///E:/M2Game/Client/assets/scripts/autogen/pb/cs_proto.mjs?cjs=&original=.js as file:///E:/M2Game/Client/assets/scripts/autogen/pb/cs_proto.js.
16:38:05.725 debug: Resolve ./cs_proto.js from file:///E:/M2Game/Client/assets/scripts/autogen/pb/cs_proto.mjs?cjs=&original=.js as file:///E:/M2Game/Client/assets/scripts/autogen/pb/cs_proto.js.
16:38:05.725 debug: Resolve cce:/internal/ml/cjs-loader.mjs from file:///E:/M2Game/Client/assets/scripts/autogen/pb/cs_proto.js as cce:/internal/ml/cjs-loader.mjs.
16:38:05.725 debug: Resolve protobufjs/minimal.js from file:///E:/M2Game/Client/assets/scripts/autogen/pb/cs_proto.js as file:///E:/M2Game/Client/node_modules/protobufjs/minimal.js.
16:38:05.725 debug: Resolve cce:/internal/ml/cjs-loader.mjs from file:///E:/M2Game/Client/node_modules/protobufjs/minimal.js as cce:/internal/ml/cjs-loader.mjs.
16:38:05.725 debug: Resolve ./src/index-minimal from file:///E:/M2Game/Client/node_modules/protobufjs/minimal.js as file:///E:/M2Game/Client/node_modules/protobufjs/src/index-minimal.js.
16:38:05.725 debug: Resolve cce:/internal/ml/cjs-loader.mjs from file:///E:/M2Game/Client/node_modules/protobufjs/src/index-minimal.js as cce:/internal/ml/cjs-loader.mjs.
16:38:05.725 debug: Resolve ./writer from file:///E:/M2Game/Client/node_modules/protobufjs/src/index-minimal.js as file:///E:/M2Game/Client/node_modules/protobufjs/src/writer.js.
16:38:05.725 debug: Resolve ./writer_buffer from file:///E:/M2Game/Client/node_modules/protobufjs/src/index-minimal.js as file:///E:/M2Game/Client/node_modules/protobufjs/src/writer_buffer.js.
16:38:05.725 debug: Resolve ./reader from file:///E:/M2Game/Client/node_modules/protobufjs/src/index-minimal.js as file:///E:/M2Game/Client/node_modules/protobufjs/src/reader.js.
16:38:05.725 debug: Resolve ./reader_buffer from file:///E:/M2Game/Client/node_modules/protobufjs/src/index-minimal.js as file:///E:/M2Game/Client/node_modules/protobufjs/src/reader_buffer.js.
16:38:05.725 debug: Resolve ./util/minimal from file:///E:/M2Game/Client/node_modules/protobufjs/src/index-minimal.js as file:///E:/M2Game/Client/node_modules/protobufjs/src/util/minimal.js.
16:38:05.725 debug: Resolve ./rpc from file:///E:/M2Game/Client/node_modules/protobufjs/src/index-minimal.js as file:///E:/M2Game/Client/node_modules/protobufjs/src/rpc.js.
16:38:05.725 debug: Resolve ./roots from file:///E:/M2Game/Client/node_modules/protobufjs/src/index-minimal.js as file:///E:/M2Game/Client/node_modules/protobufjs/src/roots.js.
16:38:05.725 debug: Resolve cce:/internal/ml/cjs-loader.mjs from file:///E:/M2Game/Client/node_modules/protobufjs/src/writer.js as cce:/internal/ml/cjs-loader.mjs.
16:38:05.725 debug: Resolve ./util/minimal from file:///E:/M2Game/Client/node_modules/protobufjs/src/writer.js as file:///E:/M2Game/Client/node_modules/protobufjs/src/util/minimal.js.
16:38:05.725 debug: Resolve cce:/internal/ml/cjs-loader.mjs from file:///E:/M2Game/Client/node_modules/protobufjs/src/util/minimal.js as cce:/internal/ml/cjs-loader.mjs.
16:38:05.725 debug: Resolve @protobufjs/aspromise from file:///E:/M2Game/Client/node_modules/protobufjs/src/util/minimal.js as file:///E:/M2Game/Client/node_modules/@protobufjs/aspromise/index.js.
16:38:05.725 debug: Resolve @protobufjs/base64 from file:///E:/M2Game/Client/node_modules/protobufjs/src/util/minimal.js as file:///E:/M2Game/Client/node_modules/@protobufjs/base64/index.js.
16:38:05.725 debug: Resolve @protobufjs/eventemitter from file:///E:/M2Game/Client/node_modules/protobufjs/src/util/minimal.js as file:///E:/M2Game/Client/node_modules/@protobufjs/eventemitter/index.js.
16:38:05.725 debug: Resolve @protobufjs/float from file:///E:/M2Game/Client/node_modules/protobufjs/src/util/minimal.js as file:///E:/M2Game/Client/node_modules/@protobufjs/float/index.js.
16:38:05.725 debug: Resolve @protobufjs/inquire from file:///E:/M2Game/Client/node_modules/protobufjs/src/util/minimal.js as file:///E:/M2Game/Client/node_modules/@protobufjs/inquire/index.js.
16:38:05.725 debug: Resolve @protobufjs/utf8 from file:///E:/M2Game/Client/node_modules/protobufjs/src/util/minimal.js as file:///E:/M2Game/Client/node_modules/@protobufjs/utf8/index.js.
16:38:05.725 debug: Resolve @protobufjs/pool from file:///E:/M2Game/Client/node_modules/protobufjs/src/util/minimal.js as file:///E:/M2Game/Client/node_modules/@protobufjs/pool/index.js.
16:38:05.725 debug: Resolve ./longbits from file:///E:/M2Game/Client/node_modules/protobufjs/src/util/minimal.js as file:///E:/M2Game/Client/node_modules/protobufjs/src/util/longbits.js.
16:38:05.725 debug: Resolve cce:/internal/ml/cjs-loader.mjs from file:///E:/M2Game/Client/node_modules/@protobufjs/aspromise/index.js as cce:/internal/ml/cjs-loader.mjs.
16:38:05.725 debug: Resolve cce:/internal/ml/cjs-loader.mjs from file:///E:/M2Game/Client/node_modules/@protobufjs/base64/index.js as cce:/internal/ml/cjs-loader.mjs.
16:38:05.725 debug: Resolve cce:/internal/ml/cjs-loader.mjs from file:///E:/M2Game/Client/node_modules/@protobufjs/eventemitter/index.js as cce:/internal/ml/cjs-loader.mjs.
16:38:05.725 debug: Resolve cce:/internal/ml/cjs-loader.mjs from file:///E:/M2Game/Client/node_modules/@protobufjs/float/index.js as cce:/internal/ml/cjs-loader.mjs.
16:38:05.725 debug: Resolve cce:/internal/ml/cjs-loader.mjs from file:///E:/M2Game/Client/node_modules/@protobufjs/inquire/index.js as cce:/internal/ml/cjs-loader.mjs.
16:38:05.725 debug: Resolve cce:/internal/ml/cjs-loader.mjs from file:///E:/M2Game/Client/node_modules/@protobufjs/utf8/index.js as cce:/internal/ml/cjs-loader.mjs.
16:38:05.725 debug: Resolve cce:/internal/ml/cjs-loader.mjs from file:///E:/M2Game/Client/node_modules/@protobufjs/pool/index.js as cce:/internal/ml/cjs-loader.mjs.
16:38:05.725 debug: Resolve cce:/internal/ml/cjs-loader.mjs from file:///E:/M2Game/Client/node_modules/protobufjs/src/util/longbits.js as cce:/internal/ml/cjs-loader.mjs.
16:38:05.725 debug: Resolve ../util/minimal from file:///E:/M2Game/Client/node_modules/protobufjs/src/util/longbits.js as file:///E:/M2Game/Client/node_modules/protobufjs/src/util/minimal.js.
16:38:05.725 debug: Resolve cce:/internal/ml/cjs-loader.mjs from file:///E:/M2Game/Client/node_modules/protobufjs/src/writer_buffer.js as cce:/internal/ml/cjs-loader.mjs.
16:38:05.725 debug: Resolve ./writer from file:///E:/M2Game/Client/node_modules/protobufjs/src/writer_buffer.js as file:///E:/M2Game/Client/node_modules/protobufjs/src/writer.js.
16:38:05.725 debug: Resolve ./util/minimal from file:///E:/M2Game/Client/node_modules/protobufjs/src/writer_buffer.js as file:///E:/M2Game/Client/node_modules/protobufjs/src/util/minimal.js.
16:38:05.725 debug: Resolve cce:/internal/ml/cjs-loader.mjs from file:///E:/M2Game/Client/node_modules/protobufjs/src/reader.js as cce:/internal/ml/cjs-loader.mjs.
16:38:05.725 debug: Resolve ./util/minimal from file:///E:/M2Game/Client/node_modules/protobufjs/src/reader.js as file:///E:/M2Game/Client/node_modules/protobufjs/src/util/minimal.js.
16:38:05.725 debug: Resolve cce:/internal/ml/cjs-loader.mjs from file:///E:/M2Game/Client/node_modules/protobufjs/src/reader_buffer.js as cce:/internal/ml/cjs-loader.mjs.
16:38:05.725 debug: Resolve ./reader from file:///E:/M2Game/Client/node_modules/protobufjs/src/reader_buffer.js as file:///E:/M2Game/Client/node_modules/protobufjs/src/reader.js.
16:38:05.725 debug: Resolve ./util/minimal from file:///E:/M2Game/Client/node_modules/protobufjs/src/reader_buffer.js as file:///E:/M2Game/Client/node_modules/protobufjs/src/util/minimal.js.
16:38:05.725 debug: Resolve cce:/internal/ml/cjs-loader.mjs from file:///E:/M2Game/Client/node_modules/protobufjs/src/rpc.js as cce:/internal/ml/cjs-loader.mjs.
16:38:05.725 debug: Resolve ./rpc/service from file:///E:/M2Game/Client/node_modules/protobufjs/src/rpc.js as file:///E:/M2Game/Client/node_modules/protobufjs/src/rpc/service.js.
