{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/editor/emitter/EmitterEditor.ts"], "names": ["_decorator", "instantiate", "input", "Input", "KeyCode", "Component", "RichText", "Node", "Vec3", "Graphics", "assetManager", "EDITOR", "Emitter", "BulletSystem", "ccclass", "playOnFocus", "executeInEditMode", "property", "disallowMultiple", "menu", "EmitterEditor", "visible", "displayName", "type", "override", "_updateInEditor", "_graphicsCom", "_player<PERSON>os", "targetFrameRate", "fixedDelta", "value", "graphics", "getComponent", "addComponent", "resetInEditor", "onFocusInEditor", "node", "walk", "emitter", "setIsActive", "onLostFocusInEditor", "reset", "onLoad", "on", "EventType", "KEY_PRESSING", "onKeyPressing", "onDestroy", "off", "event", "console", "log", "keyCode", "player<PERSON>ode", "getPosition", "KEY_W", "y", "KEY_S", "KEY_A", "x", "KEY_D", "setPosition", "start", "frameCount", "frameTimeInMilliseconds", "destroyAllBullets", "allEventGroups", "z", "update", "dt", "updateInfoText", "milli_dt", "tick", "richText", "string", "toFixed", "allEmitters", "length", "allBullets", "instantiatePrefab", "prefabUuid", "loadAny", "uuid", "err", "prefab", "parent", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAkBC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,K,OAAAA,K;AAAsBC,MAAAA,O,OAAAA,O;AAASC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,Y,OAAAA,Y;;AAChHC,MAAAA,M,UAAAA,M;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,Y,iBAAAA,Y;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,WAAX;AAAwBC,QAAAA,iBAAxB;AAA2CC,QAAAA,QAA3C;AAAqDC,QAAAA,gBAArD;AAAuEC,QAAAA;AAAvE,O,GAAiFnB,U;;+BAO1EoB,a,WALZN,OAAO,CAAC,eAAD,C,UACPK,IAAI,CAAC,aAAD,C,UACJJ,WAAW,CAAC,IAAD,C,UACXC,iBAAiB,CAAC,IAAD,C,UACjBE,gBAAgB,CAAC,IAAD,C,UAEZD,QAAQ,CAAC;AAACI,QAAAA,OAAO,EAAC;AAAT,OAAD,C,UAGRJ,QAAQ,CAAC;AAACK,QAAAA,WAAW,EAAE;AAAd,OAAD,C,UASRL,QAAQ,CAAC;AAACM,QAAAA,IAAI,EAAEjB,QAAP;AAAiBkB,QAAAA,QAAQ,EAAE,IAA3B;AAAiCF,QAAAA,WAAW,EAAE;AAA9C,OAAD,C,UAGRL,QAAQ,CAAC;AAACM,QAAAA,IAAI,EAAEhB,IAAP;AAAae,QAAAA,WAAW,EAAC;AAAzB,OAAD,C,kGArBb,MAKaF,aALb,SAKmCf,SALnC,CAK6C;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAsBjCoB,eAtBiC,GAsBN,KAtBM;AAAA,eAuBjCC,YAvBiC,GAuBH,IAvBG;AAAA,eAiEzCC,UAjEyC,GAiEtB,IAAInB,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe,CAAf,CAjEsB;AAAA;;AAEd;AAGD,YAAfoB,eAAe,GAAW;AACjC,iBAAO,OAAO,KAAKC,UAAnB;AACH;;AAEyB,YAAfD,eAAe,CAACE,KAAD,EAAgB;AACtC,eAAKD,UAAL,GAAkB,OAAOC,KAAzB;AACH;;AAakB,YAARC,QAAQ,GAAa;AAC5B,cAAI,CAAC,KAAKL,YAAV,EAAwB;AACpB,iBAAKA,YAAL,GAAoB,KAAKM,YAAL,CAAkBvB,QAAlB,KAA+B,KAAKwB,YAAL,CAAkBxB,QAAlB,CAAnD;AACH;;AACD,iBAAO,KAAKiB,YAAZ;AACH;;AAEDQ,QAAAA,aAAa,GAAG;AACZ,eAAKT,eAAL,GAAuB,IAAvB,CADY,CAEZ;AACH;;AAEDU,QAAAA,eAAe,GAAG;AACd,eAAKV,eAAL,GAAuB,IAAvB,CADc,CAEd;AAEA;AACA;AAEA;;AACA,eAAKW,IAAL,CAAUC,IAAV,CAAgBD,IAAD,IAAU;AACrB,kBAAME,OAAO,GAAGF,IAAI,CAACJ,YAAL;AAAA;AAAA,mCAAhB;;AACA,gBAAIM,OAAJ,EAAa;AACTA,cAAAA,OAAO,CAACC,WAAR,CAAoB,IAApB;AACH;AACJ,WALD;AAMH;;AAEDC,QAAAA,mBAAmB,GAAS;AACxB,eAAKf,eAAL,GAAuB,KAAvB;AACA,eAAKgB,KAAL;AACH;;AAEDC,QAAAA,MAAM,GAAG;AACLxC,UAAAA,KAAK,CAACyC,EAAN,CAASxC,KAAK,CAACyC,SAAN,CAAgBC,YAAzB,EAAuC,KAAKC,aAA5C,EAA2D,IAA3D;AACH;;AAEDC,QAAAA,SAAS,GAAG;AACR7C,UAAAA,KAAK,CAAC8C,GAAN,CAAU7C,KAAK,CAACyC,SAAN,CAAgBC,YAA1B,EAAwC,KAAKC,aAA7C,EAA4D,IAA5D;AACH;;AAGDA,QAAAA,aAAa,CAACG,KAAD,EAAuB;AAChCC,UAAAA,OAAO,CAACC,GAAR,CAAY,eAAZ,EAA6BF,KAAK,CAACG,OAAnC,EADgC,CAEhC;AAEA;;AACA,cAAI,KAAKC,UAAT,EAAqB;AACjB,iBAAKA,UAAL,CAAgBC,WAAhB,CAA4B,KAAK3B,UAAjC;;AACA,gBAAIsB,KAAK,CAACG,OAAN,KAAkBhD,OAAO,CAACmD,KAA9B,EAAqC;AACjC,mBAAK5B,UAAL,CAAgB6B,CAAhB,IAAqB,EAArB;AACH;;AACD,gBAAIP,KAAK,CAACG,OAAN,KAAkBhD,OAAO,CAACqD,KAA9B,EAAqC;AACjC,mBAAK9B,UAAL,CAAgB6B,CAAhB,IAAqB,EAArB;AACH;;AACD,gBAAIP,KAAK,CAACG,OAAN,KAAkBhD,OAAO,CAACsD,KAA9B,EAAqC;AACjC,mBAAK/B,UAAL,CAAgBgC,CAAhB,IAAqB,EAArB;AACH;;AACD,gBAAIV,KAAK,CAACG,OAAN,KAAkBhD,OAAO,CAACwD,KAA9B,EAAqC;AACjC,mBAAKjC,UAAL,CAAgBgC,CAAhB,IAAqB,EAArB;AACH;;AACD,iBAAKN,UAAL,CAAgBQ,WAAhB,CAA4B,KAAKlC,UAAjC;AACH;AACJ;;AAEDmC,QAAAA,KAAK,GAAG;AACJ,eAAKrB,KAAL;AACH;;AAEDA,QAAAA,KAAK,GAAG;AACJrB,UAAAA,aAAa,CAAC2C,UAAd,GAA2B,CAA3B;AACA3C,UAAAA,aAAa,CAAC4C,uBAAd,GAAwC,CAAxC;AACA;AAAA;AAAA,4CAAaC,iBAAb,CAA+B,IAA/B;AACA,eAAK7B,IAAL,CAAUC,IAAV,CAAgBD,IAAD,IAAU;AACrB,kBAAME,OAAO,GAAGF,IAAI,CAACJ,YAAL;AAAA;AAAA,mCAAhB;;AACA,gBAAIM,OAAJ,EAAa;AACTA,cAAAA,OAAO,CAACC,WAAR,CAAoB,KAApB;AACH;AACJ,WALD;AAMA;AAAA;AAAA,4CAAa2B,cAAb,GAA8B,EAA9B;AACA,eAAKvC,UAAL,CAAgBgC,CAAhB,GAAoB,KAAKhC,UAAL,CAAgB6B,CAAhB,GAAoB,KAAK7B,UAAL,CAAgBwC,CAAhB,GAAoB,CAA5D;;AACA,cAAI,KAAKd,UAAT,EAAqB;AACjB,iBAAKA,UAAL,CAAgBQ,WAAhB,CAA4B,KAAKlC,UAAjC;AACH;;AACDuB,UAAAA,OAAO,CAACC,GAAR,CAAY,SAAZ,EAAuB,KAAK1B,eAA5B;AACH;;AAED2C,QAAAA,MAAM,CAACC,EAAD,EAAa;AACf,cAAI1D,MAAM,IAAI,KAAKc,eAAnB,EAAoC;AAChC,iBAAK6C,cAAL;AACA,kBAAMC,QAAQ,GAAGF,EAAE,GAAG,IAAtB;AACAjD,YAAAA,aAAa,CAAC2C,UAAd,IAA4B,CAA5B;AACA3C,YAAAA,aAAa,CAAC4C,uBAAd,IAAyCO,QAAzC;AACA;AAAA;AAAA,8CAAaC,IAAb,CAAkBH,EAAlB;AACH;AACJ;;AAEDC,QAAAA,cAAc,GAAG;AACb,cAAI,KAAKG,QAAT,EAAmB;AACf,iBAAKA,QAAL,CAAcC,MAAd,GAAwB,SAAQtD,aAAa,CAAC4C,uBAAd,CAAsCW,OAAtC,CAA8C,CAA9C,CAAiD,cAAa;AAAA;AAAA,8CAAaC,WAAb,CAAyBC,MAAO,aAAY;AAAA;AAAA,8CAAaC,UAAb,CAAwBD,MAAO,cAAa;AAAA;AAAA,8CAAaX,cAAb,CAA4BW,MAAO,EAAzN;AACH;AACJ,SA7HwC,CA+HzC;;;AACOE,QAAAA,iBAAiB,CAACC,UAAD,EAAqB;AACzC;AACA;AACAtE,UAAAA,YAAY,CAACuE,OAAb,CAAqB;AAACC,YAAAA,IAAI,EAAEF;AAAP,WAArB,EAAyC,CAACG,GAAD,EAAMC,MAAN,KAAiB;AACtD,gBAAID,GAAJ,EAAS;AACLjC,cAAAA,OAAO,CAACC,GAAR,CAAY,wBAAZ,EAAsCgC,GAAtC;AACA;AACH;;AACD,kBAAM/C,IAAI,GAAGnC,WAAW,CAACmF,MAAD,CAAxB;AACA,kBAAMC,MAAM,GAAG,KAAKjD,IAApB;AACAiD,YAAAA,MAAM,CAAEC,QAAR,CAAiBlD,IAAjB;AACH,WARD;AASH,SA5IwC,CA8IzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AA9JyC,O,UAmBlC2B,U,GAAqB,C,UACrBC,uB,GAAkC,C;;;;;iBAlBrB,K;;;;;;;iBAYC,I;;;;;;;iBAGG,I", "sourcesContent": ["import { _decorator, misc, instantiate, input, Input, EventKeyboard, KeyCode, Component, RichText, Node, Vec3, Graphics, assetManager } from 'cc';\r\nimport { EDITOR } from 'cc/env';\r\nimport { Emitter } from '../../scripts/game/bullet/Emitter';\r\nimport { BulletSystem } from '../../scripts/game/bullet/BulletSystem';\r\nconst { ccclass, playOnFocus, executeInEditMode, property, disallowMultiple, menu  } = _decorator;\r\n\r\n@ccclass('EmitterEditor')\r\n@menu('子弹系统/发射器编辑器')\r\n@playOnFocus(true)\r\n@executeInEditMode(true)\r\n@disallowMultiple(true)\r\nexport class EmitterEditor extends Component {\r\n    @property({visible:false})\r\n    fixedDelta:number = 16.67; // Fixed time step (e.g., 60 FPS), 单位: 毫秒\r\n\r\n    @property({displayName: \"目标帧率\"})\r\n    public get targetFrameRate(): number {\r\n        return 1000 / this.fixedDelta;\r\n    }\r\n\r\n    public set targetFrameRate(value: number) {\r\n        this.fixedDelta = 1000 / value;\r\n    }\r\n\r\n    @property({type: RichText, override: true, displayName: \"信息显示\"})\r\n    richText: RichText = null!;\r\n\r\n    @property({type: Node, displayName:\"玩家节点\"})\r\n    playerNode: Node|null = null;\r\n\r\n    static frameCount: number = 0;\r\n    static frameTimeInMilliseconds: number = 0;\r\n\r\n    private _updateInEditor: boolean = false;\r\n    private _graphicsCom: Graphics|null = null;\r\n    public get graphics(): Graphics {\r\n        if (!this._graphicsCom) {\r\n            this._graphicsCom = this.getComponent(Graphics) || this.addComponent(Graphics);\r\n        }\r\n        return this._graphicsCom!;\r\n    }\r\n\r\n    resetInEditor() {\r\n        this._updateInEditor = true;\r\n        // console.log('resetInEditor');\r\n    }\r\n\r\n    onFocusInEditor() {\r\n        this._updateInEditor = true;\r\n        // console.log('onFocusInEditor');\r\n        \r\n        // @ts-ignore\r\n        // Editor.Selection.select('node', BulletSystem.allEmitters.map(emitter => emitter.node.uuid));\r\n\r\n        // loop all children to find emitters\r\n        this.node.walk((node) => {\r\n            const emitter = node.getComponent(Emitter);\r\n            if (emitter) {\r\n                emitter.setIsActive(true);\r\n            }\r\n        });\r\n    }\r\n\r\n    onLostFocusInEditor(): void {\r\n        this._updateInEditor = false;\r\n        this.reset();\r\n    }\r\n\r\n    onLoad() {\r\n        input.on(Input.EventType.KEY_PRESSING, this.onKeyPressing, this);\r\n    }\r\n\r\n    onDestroy() {\r\n        input.off(Input.EventType.KEY_PRESSING, this.onKeyPressing, this);\r\n    }\r\n\r\n    _playerPos: Vec3 = new Vec3(0, 0, 0);\r\n    onKeyPressing(event: EventKeyboard) {\r\n        console.log('key pressing:', event.keyCode);\r\n        // if (!this._updateInEditor) return;\r\n\r\n        // use wasd to move player sprite(which is not present right now)\r\n        if (this.playerNode) {\r\n            this.playerNode.getPosition(this._playerPos);\r\n            if (event.keyCode === KeyCode.KEY_W) {\r\n                this._playerPos.y += 10;\r\n            }\r\n            if (event.keyCode === KeyCode.KEY_S) {\r\n                this._playerPos.y -= 10;\r\n            }\r\n            if (event.keyCode === KeyCode.KEY_A) {\r\n                this._playerPos.x -= 10;\r\n            }\r\n            if (event.keyCode === KeyCode.KEY_D) {\r\n                this._playerPos.x += 10;\r\n            }\r\n            this.playerNode.setPosition(this._playerPos);\r\n        }\r\n    }\r\n\r\n    start() {\r\n        this.reset();\r\n    }\r\n\r\n    reset() {\r\n        EmitterEditor.frameCount = 0;\r\n        EmitterEditor.frameTimeInMilliseconds = 0;\r\n        BulletSystem.destroyAllBullets(true);                \r\n        this.node.walk((node) => {\r\n            const emitter = node.getComponent(Emitter);\r\n            if (emitter) {\r\n                emitter.setIsActive(false);\r\n            }\r\n        });\r\n        BulletSystem.allEventGroups = [];\r\n        this._playerPos.x = this._playerPos.y = this._playerPos.z = 0;\r\n        if (this.playerNode) {\r\n            this.playerNode.setPosition(this._playerPos);\r\n        }\r\n        console.log('reset: ', this._updateInEditor);\r\n    }\r\n\r\n    update(dt: number) {\r\n        if (EDITOR && this._updateInEditor) {\r\n            this.updateInfoText();\r\n            const milli_dt = dt * 1000;\r\n            EmitterEditor.frameCount += 1;\r\n            EmitterEditor.frameTimeInMilliseconds += milli_dt;\r\n            BulletSystem.tick(dt);\r\n        }\r\n    }\r\n\r\n    updateInfoText() {\r\n        if (this.richText) {\r\n            this.richText.string = `当前时间: ${EmitterEditor.frameTimeInMilliseconds.toFixed(2)}\\n当前发射器数量: ${BulletSystem.allEmitters.length}\\n当前子弹数量: ${BulletSystem.allBullets.length}\\n当前事件组数量: ${BulletSystem.allEventGroups.length}`;\r\n        }\r\n    }\r\n\r\n    // 编辑器方法\r\n    public instantiatePrefab(prefabUuid: string) {\r\n        // replace db://assets/resources/game/prefabs/emitter/ with assets/resources/game/prefabs/emitter/\r\n        //prefabUrl = prefabUrl.replace('db://', '');\r\n        assetManager.loadAny({uuid: prefabUuid}, (err, prefab) => {\r\n            if (err) {\r\n                console.log('Failed to load prefab:', err);\r\n                return;\r\n            }\r\n            const node = instantiate(prefab!);\r\n            const parent = this.node;\r\n            parent!.addChild(node);\r\n        });\r\n    }\r\n\r\n    // public saveToPrefab(nodeUuid: string, prefabUrl: string): Promise<string> {\r\n    //     console.log('saveToPrefab in Component:', nodeUuid, prefabUrl);        \r\n    //     return new Promise<string>((resolve, reject) => {\r\n    //         const scene = director.getScene();\r\n    //         const target = scene!.getChildByUuid(nodeUuid);\r\n    //         if (!target) {\r\n    //             console.error(\"node not found:\", nodeUuid);\r\n    //             reject();\r\n    //             return;\r\n    //         }\r\n    //         const json = cce.Utils.serialize(target);\r\n    //         // 将节点保存为 Prefab\r\n    //         // _utils.applyTargetOverrides(target as Node);\r\n    //         // Editor.Message.request('asset-db', 'save-asset', prefabUrl, json);\r\n    //         resolve(json);\r\n    //     });\r\n    // }\r\n}\r\n"]}