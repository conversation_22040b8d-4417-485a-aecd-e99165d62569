"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.methods = void 0;
exports.load = load;
exports.unload = unload;
const cc_1 = require("cc");
/**
 * @en Registration method for the main process of Extension
 * @zh 为扩展的主进程的注册方法
 */
exports.methods = {
    /**
     * @en A method that can be triggered by message
     * @zh 通过 message 触发的方法
     */
    movePlayerUp() {
        console.log('movePlayerUp');
        Editor.Message.request('scene', 'execute-scene-script', {
            name: 'emitter-editor',
            method: 'movePlayer',
            args: [new cc_1.Vec3(0, 10, 0)]
        });
    },
    movePlayerDown() {
        console.log('movePlayerDown');
        Editor.Message.request('scene', 'execute-scene-script', {
            name: 'emitter-editor',
            method: 'movePlayer',
            args: [new cc_1.Vec3(0, -10, 0)]
        });
    },
    movePlayerLeft() {
        console.log('movePlayerLeft');
        Editor.Message.request('scene', 'execute-scene-script', {
            name: 'emitter-editor',
            method: 'movePlayer',
            args: [new cc_1.Vec3(-10, 0, 0)]
        });
    },
    movePlayerRight() {
        console.log('movePlayerRight');
        Editor.Message.request('scene', 'execute-scene-script', {
            name: 'emitter-editor',
            method: 'movePlayer',
            args: [new cc_1.Vec3(10, 0, 0)]
        });
    },
    testMessage() {
        console.log('Test message received!');
        return 'Test successful';
    }
};
/**
 * @en Method Triggered on Extension Startup
 * @zh 扩展启动时触发的方法
 */
function load() {
    console.log('emitter-editor extension loaded');
    console.log('Available methods:', Object.keys(exports.methods));
}
/**
 * @en Method triggered when uninstalling the extension
 * @zh 卸载扩展时触发的方法
 */
function unload() { }
//# sourceMappingURL=data:application/json;base64,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