{"$schema": "./@types/schema/package/index.json", "package_version": 2, "name": "emitter-editor", "version": "1.0.0", "author": "Cocos Creator", "editor": ">=3.8.6", "scripts": {"preinstall": "node ./scripts/preinstall.js", "build": "tsc", "watch": "tsc -w"}, "description": "i18n:emitter-editor.description", "main": "./dist/main.js", "devDependencies": {"@cocos/creator-types": "^3.8.6", "@types/node": "^18.17.1", "typescript": "^5.8.2"}, "contributions": {"inspector": {"section": {"node": {"EmitterEditor": "./dist/contributions/inspector/emitter-editor.js", "Emitter": "./dist/contributions/inspector/emitter.js"}}}, "scene": {"script": "./dist/scene.js"}, "messages": {"emitter-editor:up": {"methods": ["movePlayerUp"]}, "move-player-down": {"methods": ["movePlayerDown"]}, "move-player-left": {"methods": ["movePlayerLeft"]}, "move-player-right": {"methods": ["movePlayerRight"]}, "test-message": {"methods": ["testMessage"]}}, "shortcuts": [{"message": "emitter-editor:up", "win": "ctrl+shift+w", "mac": "cmd+shift+w"}, {"message": "move-player-down", "win": "ctrl+shift+s", "mac": "cmd+shift+s"}, {"message": "move-player-left", "win": "ctrl+shift+a", "mac": "cmd+shift+a"}, {"message": "move-player-right", "win": "ctrl+shift+d", "mac": "cmd+shift+d"}, {"message": "test-message", "win": "ctrl+shift+t", "mac": "cmd+shift+t"}]}}