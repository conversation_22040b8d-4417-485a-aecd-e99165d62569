{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/autogen/luban/schema.ts"], "names": ["A<PERSON><PERSON><PERSON><PERSON>", "ConParam", "ConsumeItem", "ConsumeMoney", "EffectParam", "EquipProp", "GM", "PlaneEffect", "PlaneMaterial", "PlaneProperty", "PropInc", "randStrategy", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ResBoss", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ResBullet", "ResChapter", "ResEffect", "ResEnemy", "ResEquip", "ResEquipUpgrade", "ResGameMode", "ResGlobalAttr", "ResItem", "ResLevel", "ResLevelGroup", "ResMainPlane", "ResMainPlaneLv", "ResPlane", "ResSkill", "ResStage", "ResTask", "ResTrack", "ResWave", "SkillCondition", "TbGlobalAttr", "TbEquipUpgrade", "TbGM", "TbPlane", "TbResBoss", "TbRes<PERSON>uffer", "TbResBullet", "TbResChapter", "TbResEffect", "TbResEnemy", "TbResEquip", "TbResGameMode", "TbResItem", "TbResLevel", "TbResLevelGroup", "TbResMainPlane", "TbResMainPlaneLv", "TbResSkill", "TbResStage", "TbResTask", "TbResTrack", "TbResWave", "Tables", "BuffType", "BulletSourceType", "BulletType", "EffectType", "EquipClass", "GMTabID", "ItemEffectType", "ItemUseType", "ModeType", "MoneyType", "PlayCycle", "PropName", "QualityType", "SkillConditionType", "TargetType", "TaskClass", "TaskCondType", "TaskObjectType", "TaskPeriodType", "constructor", "_json_", "target", "buff<PERSON>", "undefined", "Error", "resolve", "tables", "vector2", "x", "y", "builtin", "vector3", "z", "vector4", "w", "con", "param", "id", "num", "type", "_ele0", "_e0", "push", "value", "tabID", "tabName", "name", "cmd", "desc", "effectId", "effect_id", "materialId", "materialCount", "material_id", "material_count", "propType", "propValue", "prop_type", "prop_value", "inc", "ID", "Weight", "rating", "bId", "sId", "app", "ta", "ft", "leave", "exp", "rid", "sk", "blp", "us", "ua", "va", "sv", "fl", "loot", "adsorb", "lp0", "lp1", "dh", "atk", "col", "tway", "way", "wi", "sp", "ai", "ra", "a0", "a1", "a2", "a3", "a4", "a5", "a6", "a7", "a8", "a9", "a10", "a11", "a12", "a13", "a14", "a15", "a16", "a17", "a18", "a19", "a20", "a21", "a22", "a100", "a101", "buffType", "duration", "durationBonus", "maxStack", "refreshType", "cycle", "cycleTimes", "effects", "conditionID", "source", "prefab", "attackCoefficient", "attack_coefficient", "levelCount", "levelGroupCount", "strategy", "damageBonus", "life<PERSON><PERSON><PERSON>", "strategyList", "_e", "description", "icon", "effectType", "effectValue", "effectParams", "effect_type", "effect_value", "effect_params", "uiId", "hp", "collideLevel", "turn", "hpShow", "collide<PERSON><PERSON><PERSON>", "bCollideDead", "bMoveAttack", "bStayAttack", "attackInterval", "attackNum", "attackData", "dieShoot", "dieBullet", "quality", "qualitySub", "equipClass", "props", "consumeItems", "quality_sub", "equip_class", "consume_items", "levelFrom", "levelTo", "propInc", "consumeMoney", "level_from", "level_to", "prop_inc", "consume_money", "modeType", "chapterID", "order", "resourceID", "conList", "times", "monType", "costParam1", "costParam2", "rebirthTimes", "rebirthCost", "power", "rogueID", "LevelLimit", "rogueFirst", "sweepLimit", "rewardID1", "rewardID2", "ratingList", "GoldProducion", "MaxEnergy", "EnergyRecoverInterval", "EnergyRecoverValue", "ItemPickUpRadius", "PostHitProtection", "useType", "effectParam1", "effectParam2", "maxStack<PERSON>um", "use_type", "effect_param1", "effect_param2", "max_stack_num", "forbidFire", "forbidNBomb", "forbidActSkill", "planeCollisionScaling", "levelType", "normLevelCount", "normLevelST", "normSTList", "bossLevelCount", "bossLevelST", "bossSTList", "body", "transSrc", "transExt", "zjdmtxzb", "transatk1", "shiftingatk1", "starLevel", "portrait", "properties", "materials", "star_level", "cd", "CostID", "CostNum", "ApplyBuffs", "mainStage", "subStage", "enemyGroupID", "delay", "enemyNorRate", "taskId", "groupId", "taskClass", "prevId", "periodType", "openType", "openValue", "goalType", "goalParams", "accumulate", "rewardId", "orbitId", "orbitValue", "openDate", "openTime", "closeDate", "closeTime", "task_id", "group_id", "task_class", "prev_id", "period_type", "open_type", "open_value", "goal_type", "goal_params", "reward_id", "orbit_id", "orbit_value", "open_date", "open_time", "close_date", "close_time", "tpe", "planeType", "planeId", "interval", "offsetPos", "pos", "track", "trackParams", "rotatioSpeed", "FirstShootDelay", "_data", "length", "getData", "_dataList", "_json2_", "_v", "getDataList", "get", "index", "data", "_dataMap", "Map", "set", "getDataMap", "key", "_TbGlobalAttr", "_TbEquipUpgrade", "_TbGM", "_TbPlane", "_TbResBoss", "_Tb<PERSON>es<PERSON>uffer", "_TbResBullet", "_TbResChapter", "_TbResEffect", "_TbResEnemy", "_TbResEquip", "_TbResGameMode", "_TbResItem", "_TbResLevel", "_TbResLevelGroup", "_TbResMainPlane", "_TbResMainPlaneLv", "_TbResSkill", "_TbResStage", "_TbResTask", "_TbResTrack", "_TbResWave", "loader"], "mappings": ";;;iBA+rBaA,S,EAoGAC,Q,EAyBAC,W,EAsBAC,Y,EA4BAC,W,EA6BAC,S,EAsBAC,E,EAoDAC,W,EAqBAC,a,EAyBAC,a,EAyBAC,O,EA4BAC,Y,EA4BAC,W,EAsBAC,O,EAwYAC,S,EAiFAC,S,EAwDAC,U,EA4DAC,S,EA+DAC,Q,EAqIAC,Q,EAsEAC,e,EAwDAC,W,EAkKAC,a,EAwDAC,O,EAoFAC,Q,EA+DAC,a,EAyDAC,Y,EAsEAC,c,EAmCAC,Q,EAoFAC,Q,EA0EAC,Q,EA+DAC,O,EAqIAC,Q,EAmCAC,O,EAyGAC,c,EAuBAC,Y,EA6CAC,c,EA4BAC,I,EA4BAC,O,EA4BAC,S,EA+BAC,W,EA+BAC,W,EA+BAC,Y,EA+BAC,W,EA+BAC,U,EA+BAC,U,EA+BAC,a,EA+BAC,S,EA+BAC,U,EA+BAC,e,EA+BAC,c,EA+BAC,gB,EA+BAC,U,EA+BAC,U,EA+BAC,S,EA+BAC,U,EA+BAC,S,EAiCAC,M;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA7rHb;AACA;AACA;AACA;AACA;AACA;AACA;;AAIA;AACA;AACA;0BACYC,Q,0BAAAA,Q;AAAAA,QAAAA,Q,CAAAA,Q;AAAAA,QAAAA,Q,CAAAA,Q;AAAAA,QAAAA,Q,CAAAA,Q;eAAAA,Q;;AAiBZ;AACA;AACA;;;kCACYC,gB,0BAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;eAAAA,gB;;AAiBZ;AACA;AACA;;;4BACYC,U,0BAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;eAAAA,U;;AAaZ;AACA;AACA;;;4BACYC,U,0BAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;eAAAA,U;;AAqLZ;AACA;AACA;;;4BACYC,U,0BAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;eAAAA,U;;AAyBZ;AACA;AACA;;;yBACYC,O,0BAAAA,O;AAAAA,QAAAA,O,CAAAA,O;AAAAA,QAAAA,O,CAAAA,O;eAAAA,O;;AAaZ;AACA;AACA;;;gCACYC,c,0BAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;eAAAA,c;;AAiCZ;AACA;AACA;;;6BACYC,W,0BAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;eAAAA,W;;AAiBZ;AACA;AACA;;;0BACYC,Q,0BAAAA,Q;AAAAA,QAAAA,Q,CAAAA,Q;AAAAA,QAAAA,Q,CAAAA,Q;AAAAA,QAAAA,Q,CAAAA,Q;AAAAA,QAAAA,Q,CAAAA,Q;AAAAA,QAAAA,Q,CAAAA,Q;eAAAA,Q;;AAyBZ;AACA;AACA;;;2BACYC,S,0BAAAA,S;AAAAA,QAAAA,S,CAAAA,S;AAAAA,QAAAA,S,CAAAA,S;AAAAA,QAAAA,S,CAAAA,S;AAAAA,QAAAA,S,CAAAA,S;AAAAA,QAAAA,S,CAAAA,S;eAAAA,S;;AAyBZ;AACA;AACA;;;2BACYC,S,0BAAAA,S;AAAAA,QAAAA,S,CAAAA,S;AAAAA,QAAAA,S,CAAAA,S;eAAAA,S;;AAaZ;AACA;AACA;;;0BACYC,Q,0BAAAA,Q;AAAAA,QAAAA,Q,CAAAA,Q;AAAAA,QAAAA,Q,CAAAA,Q;AAAAA,QAAAA,Q,CAAAA,Q;eAAAA,Q;;AAiBZ;AACA;AACA;;;6BACYC,W,0BAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;eAAAA,W;;AAiCZ;AACA;AACA;;;oCACYC,kB,0BAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;eAAAA,kB;;AASZ;AACA;AACA;;;4BACYC,U,0BAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;eAAAA,U;;;2BA6BAC,S,0BAAAA,S;AAAAA,QAAAA,S,CAAAA,S;AAAAA,QAAAA,S,CAAAA,S;AAAAA,QAAAA,S,CAAAA,S;AAAAA,QAAAA,S,CAAAA,S;AAAAA,QAAAA,S,CAAAA,S;eAAAA,S;;;8BAyBAC,Y,0BAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;eAAAA,Y;;;gCAyBAC,c,0BAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;eAAAA,c;;;gCAyGAC,c,0BAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;eAAAA,c;;;2BAyBC5E,S,GAAN,MAAMA,SAAN,CAAgB;AAEnB6E,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAOhBC,MAPgB;AAAA,eAQhBC,MARgB;;AACrB,cAAIF,MAAM,CAACC,MAAP,KAAkBE,SAAtB,EAAiC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD,eAAKH,MAAL,GAAcD,MAAM,CAACC,MAArB;;AACA,cAAID,MAAM,CAACE,MAAP,KAAkBC,SAAtB,EAAiC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD,eAAKF,MAAL,GAAcF,MAAM,CAACE,MAArB;AACH;;AAKDG,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAGtB;;AAfkB,O;;;AAsBhB,cAAMC,OAAN,CAAc;AAEjBR,UAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,iBAOhBQ,CAPgB;AAAA,iBAQhBC,CARgB;;AACrB,gBAAIT,MAAM,CAACQ,CAAP,KAAaL,SAAjB,EAA4B;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACjD,iBAAKI,CAAL,GAASR,MAAM,CAACQ,CAAhB;;AACA,gBAAIR,MAAM,CAACS,CAAP,KAAaN,SAAjB,EAA4B;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACjD,iBAAKK,CAAL,GAAST,MAAM,CAACS,CAAhB;AACH;;AAKDJ,UAAAA,OAAO,CAACC,MAAD,EAAgB,CAGtB;;AAfgB;;;SADJI,O,uBAAAA,O;;;AAuBV,cAAMC,OAAN,CAAc;AAEjBZ,UAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,iBAShBQ,CATgB;AAAA,iBAUhBC,CAVgB;AAAA,iBAWhBG,CAXgB;;AACrB,gBAAIZ,MAAM,CAACQ,CAAP,KAAaL,SAAjB,EAA4B;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACjD,iBAAKI,CAAL,GAASR,MAAM,CAACQ,CAAhB;;AACA,gBAAIR,MAAM,CAACS,CAAP,KAAaN,SAAjB,EAA4B;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACjD,iBAAKK,CAAL,GAAST,MAAM,CAACS,CAAhB;;AACA,gBAAIT,MAAM,CAACY,CAAP,KAAaT,SAAjB,EAA4B;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACjD,iBAAKQ,CAAL,GAASZ,MAAM,CAACY,CAAhB;AACH;;AAMDP,UAAAA,OAAO,CAACC,MAAD,EAAgB,CAItB;;AAnBgB;;;SADJI,O,uBAAAA,O;;;AA2BV,cAAMG,OAAN,CAAc;AAEjBd,UAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,iBAWhBQ,CAXgB;AAAA,iBAYhBC,CAZgB;AAAA,iBAahBG,CAbgB;AAAA,iBAchBE,CAdgB;;AACrB,gBAAId,MAAM,CAACQ,CAAP,KAAaL,SAAjB,EAA4B;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACjD,iBAAKI,CAAL,GAASR,MAAM,CAACQ,CAAhB;;AACA,gBAAIR,MAAM,CAACS,CAAP,KAAaN,SAAjB,EAA4B;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACjD,iBAAKK,CAAL,GAAST,MAAM,CAACS,CAAhB;;AACA,gBAAIT,MAAM,CAACY,CAAP,KAAaT,SAAjB,EAA4B;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACjD,iBAAKQ,CAAL,GAASZ,MAAM,CAACY,CAAhB;;AACA,gBAAIZ,MAAM,CAACc,CAAP,KAAaX,SAAjB,EAA4B;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACjD,iBAAKU,CAAL,GAASd,MAAM,CAACc,CAAhB;AACH;;AAODT,UAAAA,OAAO,CAACC,MAAD,EAAgB,CAKtB;;AAvBgB;;;SADJI,O,uBAAAA,O;;0BA+BJvF,Q,GAAN,MAAMA,QAAN,CAAe;AAElB4E,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAOhBe,GAPgB;AAAA,eAQhBC,KARgB;;AACrB,cAAIhB,MAAM,CAACe,GAAP,KAAeZ,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKW,GAAL,GAAWf,MAAM,CAACe,GAAlB;;AACA,cAAIf,MAAM,CAACgB,KAAP,KAAiBb,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAKY,KAAL,GAAahB,MAAM,CAACgB,KAApB;AACH;;AAKDX,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAGtB;;AAfiB,O;AAsBtB;AACA;AACA;;;6BACalF,W,GAAN,MAAMA,WAAN,CAAkB;AAErB2E,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAOhBiB,EAPgB;AAAA,eAQhBC,GARgB;;AACrB,cAAIlB,MAAM,CAACiB,EAAP,KAAcd,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKa,EAAL,GAAUjB,MAAM,CAACiB,EAAjB;;AACA,cAAIjB,MAAM,CAACkB,GAAP,KAAef,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKc,GAAL,GAAWlB,MAAM,CAACkB,GAAlB;AACH;;AAKDb,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAGtB;;AAfoB,O;;8BAsBZjF,Y,GAAN,MAAMA,YAAN,CAAmB;AAEtB0E,QAAAA,WAAW,CAACC,MAAD,EAAc;AAOzB;AACJ;AACA;AAT6B,eAUhBmB,IAVgB;;AAWzB;AACJ;AACA;AAb6B,eAchBD,GAdgB;;AACrB,cAAIlB,MAAM,CAACmB,IAAP,KAAgBhB,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKe,IAAL,GAAYnB,MAAM,CAACmB,IAAnB;;AACA,cAAInB,MAAM,CAACkB,GAAP,KAAef,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKc,GAAL,GAAWlB,MAAM,CAACkB,GAAlB;AACH;;AAWDb,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAGtB;;AArBqB,O;;6BA4BbhF,W,GAAN,MAAMA,WAAN,CAAkB;AAErByE,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAShBmB,IATgB;AAAA,eAUhBlB,MAVgB;AAAA,eAWhBe,KAXgB;;AACrB,cAAIhB,MAAM,CAACmB,IAAP,KAAgBhB,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKe,IAAL,GAAYnB,MAAM,CAACmB,IAAnB;;AACA,cAAInB,MAAM,CAACC,MAAP,KAAkBE,SAAtB,EAAiC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD,eAAKH,MAAL,GAAcD,MAAM,CAACC,MAArB;;AACA,cAAID,MAAM,CAACgB,KAAP,KAAiBb,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD;AAAE,iBAAKY,KAAL,GAAa,EAAb;;AAAiB,iBAAI,IAAII,KAAR,IAAiBpB,MAAM,CAACgB,KAAxB,EAA+B;AAAE,kBAAIK,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAKJ,KAAL,CAAWM,IAAX,CAAgBD,GAAhB;AAAsB;AAAC;AACpG;;AAMDhB,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAItB;;AAnBoB,O;AA0BzB;AACA;AACA;;;2BACa/E,S,GAAN,MAAMA,SAAN,CAAgB;AAEnBwE,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAOhBiB,EAPgB;AAAA,eAQhBM,KARgB;;AACrB,cAAIvB,MAAM,CAACiB,EAAP,KAAcd,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKa,EAAL,GAAUjB,MAAM,CAACiB,EAAjB;;AACA,cAAIjB,MAAM,CAACuB,KAAP,KAAiBpB,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAKmB,KAAL,GAAavB,MAAM,CAACuB,KAApB;AACH;;AAKDlB,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAGtB;;AAfkB,O;;oBAsBV9E,E,GAAN,MAAMA,EAAN,CAAS;AAEZuE,QAAAA,WAAW,CAACC,MAAD,EAAc;AAazB;AACJ;AACA;AAf6B,eAgBhBwB,KAhBgB;;AAiBzB;AACJ;AACA;AAnB6B,eAoBhBC,OApBgB;;AAqBzB;AACJ;AACA;AAvB6B,eAwBhBC,IAxBgB;;AAyBzB;AACJ;AACA;AA3B6B,eA4BhBC,GA5BgB;;AA6BzB;AACJ;AACA;AA/B6B,eAgChBC,IAhCgB;;AACrB,cAAI5B,MAAM,CAACwB,KAAP,KAAiBrB,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAKoB,KAAL,GAAaxB,MAAM,CAACwB,KAApB;;AACA,cAAIxB,MAAM,CAACyB,OAAP,KAAmBtB,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAKqB,OAAL,GAAezB,MAAM,CAACyB,OAAtB;;AACA,cAAIzB,MAAM,CAAC0B,IAAP,KAAgBvB,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKsB,IAAL,GAAY1B,MAAM,CAAC0B,IAAnB;;AACA,cAAI1B,MAAM,CAAC2B,GAAP,KAAexB,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKuB,GAAL,GAAW3B,MAAM,CAAC2B,GAAlB;;AACA,cAAI3B,MAAM,CAAC4B,IAAP,KAAgBzB,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKwB,IAAL,GAAY5B,MAAM,CAAC4B,IAAnB;AACH;;AAuBDvB,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAMtB;;AA1CW,O;AAiDhB;AACA;AACA;;;6BACa7E,W,GAAN,MAAMA,WAAN,CAAkB;AAErBsE,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAKhB6B,QALgB;;AACrB,cAAI7B,MAAM,CAAC8B,SAAP,KAAqB3B,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKyB,QAAL,GAAgB7B,MAAM,CAAC8B,SAAvB;AACH;;AAIDzB,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAEtB;;AAXoB,O;AAkBzB;AACA;AACA;;;+BACa5E,a,GAAN,MAAMA,aAAN,CAAoB;AAEvBqE,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAOhB+B,UAPgB;AAAA,eAQhBC,aARgB;;AACrB,cAAIhC,MAAM,CAACiC,WAAP,KAAuB9B,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAK2B,UAAL,GAAkB/B,MAAM,CAACiC,WAAzB;;AACA,cAAIjC,MAAM,CAACkC,cAAP,KAA0B/B,SAA9B,EAAyC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC9D,eAAK4B,aAAL,GAAqBhC,MAAM,CAACkC,cAA5B;AACH;;AAKD7B,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAGtB;;AAfsB,O;AAsB3B;AACA;AACA;;;+BACa3E,a,GAAN,MAAMA,aAAN,CAAoB;AAEvBoE,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAOhBmC,QAPgB;AAAA,eAQhBC,SARgB;;AACrB,cAAIpC,MAAM,CAACqC,SAAP,KAAqBlC,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAK+B,QAAL,GAAgBnC,MAAM,CAACqC,SAAvB;;AACA,cAAIrC,MAAM,CAACsC,UAAP,KAAsBnC,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKgC,SAAL,GAAiBpC,MAAM,CAACsC,UAAxB;AACH;;AAKDjC,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAGtB;;AAfsB,O;AAsB3B;AACA;AACA;;;yBACa1E,O,GAAN,MAAMA,OAAN,CAAc;AAEjBmE,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAOhBiB,EAPgB;;AAQzB;AACJ;AACA;AAV6B,eAWhBsB,GAXgB;;AACrB,cAAIvC,MAAM,CAACiB,EAAP,KAAcd,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKa,EAAL,GAAUjB,MAAM,CAACiB,EAAjB;;AACA,cAAIjB,MAAM,CAACuC,GAAP,KAAepC,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKmC,GAAL,GAAWvC,MAAM,CAACuC,GAAlB;AACH;;AAQDlC,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAGtB;;AAlBgB,O;AAyBrB;AACA;AACA;;;8BACazE,Y,GAAN,MAAMA,YAAN,CAAmB;AAEtBkE,QAAAA,WAAW,CAACC,MAAD,EAAc;AAOzB;AACJ;AACA;AAT6B,eAUhBwC,EAVgB;;AAWzB;AACJ;AACA;AAb6B,eAchBC,MAdgB;;AACrB,cAAIzC,MAAM,CAACwC,EAAP,KAAcrC,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKoC,EAAL,GAAUxC,MAAM,CAACwC,EAAjB;;AACA,cAAIxC,MAAM,CAACyC,MAAP,KAAkBtC,SAAtB,EAAiC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD,eAAKqC,MAAL,GAAczC,MAAM,CAACyC,MAArB;AACH;;AAWDpC,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAGtB;;AArBqB,O;;6BA4BbxE,W,GAAN,MAAMA,WAAN,CAAkB;AAErBiE,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAOhB0C,MAPgB;AAAA,eAQhB1B,KARgB;;AACrB,cAAIhB,MAAM,CAAC0C,MAAP,KAAkBvC,SAAtB,EAAiC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD,eAAKsC,MAAL,GAAc1C,MAAM,CAAC0C,MAArB;;AACA,cAAI1C,MAAM,CAACgB,KAAP,KAAiBb,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAKY,KAAL,GAAahB,MAAM,CAACgB,KAApB;AACH;;AAKDX,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAGtB;;AAfoB,O;;yBAsBZvE,O,GAAN,MAAMA,OAAN,CAAc;AAEjBgE,QAAAA,WAAW,CAACC,MAAD,EAAc;AA+GzB;AACJ;AACA;AAjH6B,eAkHhBiB,EAlHgB;;AAmHzB;AACJ;AACA;AArH6B,eAsHhB0B,GAtHgB;;AAuHzB;AACJ;AACA;AAzH6B,eA0HhBC,GA1HgB;;AA2HzB;AACJ;AACA;AA7H6B,eA8HhBC,GA9HgB;;AA+HzB;AACJ;AACA;AAjI6B,eAkIhBC,EAlIgB;;AAmIzB;AACJ;AACA;AArI6B,eAsIhBC,EAtIgB;;AAuIzB;AACJ;AACA;AAzI6B,eA0IhBC,KA1IgB;;AA2IzB;AACJ;AACA;AA7I6B,eA8IhBC,GA9IgB;;AA+IzB;AACJ;AACA;AAjJ6B,eAkJhBC,GAlJgB;;AAmJzB;AACJ;AACA;AArJ6B,eAsJhBC,EAtJgB;;AAuJzB;AACJ;AACA;AAzJ6B,eA0JhBC,GA1JgB;;AA2JzB;AACJ;AACA;AA7J6B,eA8JhBC,EA9JgB;;AA+JzB;AACJ;AACA;AAjK6B,eAkKhBC,EAlKgB;;AAmKzB;AACJ;AACA;AArK6B,eAsKhBC,EAtKgB;;AAuKzB;AACJ;AACA;AAzK6B,eA0KhBC,EA1KgB;;AA2KzB;AACJ;AACA;AA7K6B,eA8KhBC,EA9KgB;;AA+KzB;AACJ;AACA;AAjL6B,eAkLhBC,IAlLgB;;AAmLzB;AACJ;AACA;AArL6B,eAsLhBC,MAtLgB;;AAuLzB;AACJ;AACA;AAzL6B,eA0LhBC,GA1LgB;;AA2LzB;AACJ;AACA;AA7L6B,eA8LhBC,GA9LgB;;AA+LzB;AACJ;AACA;AAjM6B,eAkMhBC,EAlMgB;;AAmMzB;AACJ;AACA;AArM6B,eAsMhBC,GAtMgB;;AAuMzB;AACJ;AACA;AAzM6B,eA0MhBC,GA1MgB;;AA2MzB;AACJ;AACA;AA7M6B,eA8MhBC,IA9MgB;;AA+MzB;AACJ;AACA;AAjN6B,eAkNhBC,GAlNgB;;AAmNzB;AACJ;AACA;AArN6B,eAsNhBC,EAtNgB;;AAuNzB;AACJ;AACA;AAzN6B,eA0NhBC,EA1NgB;;AA2NzB;AACJ;AACA;AA7N6B,eA8NhBC,EA9NgB;;AA+NzB;AACJ;AACA;AAjO6B,eAkOhBC,EAlOgB;;AAmOzB;AACJ;AACA;AArO6B,eAsOhBC,EAtOgB;;AAuOzB;AACJ;AACA;AAzO6B,eA0OhBC,EA1OgB;;AA2OzB;AACJ;AACA;AA7O6B,eA8OhBC,EA9OgB;;AA+OzB;AACJ;AACA;AAjP6B,eAkPhBC,EAlPgB;;AAmPzB;AACJ;AACA;AArP6B,eAsPhBC,EAtPgB;;AAuPzB;AACJ;AACA;AAzP6B,eA0PhBC,EA1PgB;;AA2PzB;AACJ;AACA;AA7P6B,eA8PhBC,EA9PgB;;AA+PzB;AACJ;AACA;AAjQ6B,eAkQhBC,EAlQgB;;AAmQzB;AACJ;AACA;AArQ6B,eAsQhBC,EAtQgB;;AAuQzB;AACJ;AACA;AAzQ6B,eA0QhBC,EA1QgB;;AA2QzB;AACJ;AACA;AA7Q6B,eA8QhBC,GA9QgB;;AA+QzB;AACJ;AACA;AAjR6B,eAkRhBC,GAlRgB;;AAmRzB;AACJ;AACA;AArR6B,eAsRhBC,GAtRgB;;AAuRzB;AACJ;AACA;AAzR6B,eA0RhBC,GA1RgB;;AA2RzB;AACJ;AACA;AA7R6B,eA8RhBC,GA9RgB;;AA+RzB;AACJ;AACA;AAjS6B,eAkShBC,GAlSgB;;AAmSzB;AACJ;AACA;AArS6B,eAsShBC,GAtSgB;;AAuSzB;AACJ;AACA;AAzS6B,eA0ShBC,GA1SgB;;AA2SzB;AACJ;AACA;AA7S6B,eA8ShBC,GA9SgB;;AA+SzB;AACJ;AACA;AAjT6B,eAkThBC,GAlTgB;;AAmTzB;AACJ;AACA;AArT6B,eAsThBC,GAtTgB;;AAuTzB;AACJ;AACA;AAzT6B,eA0ThBC,GA1TgB;;AA2TzB;AACJ;AACA;AA7T6B,eA8ThBC,GA9TgB;;AA+TzB;AACJ;AACA;AAjU6B,eAkUhBC,IAlUgB;;AAmUzB;AACJ;AACA;AArU6B,eAsUhBC,IAtUgB;;AACrB,cAAI/F,MAAM,CAACiB,EAAP,KAAcd,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKa,EAAL,GAAUjB,MAAM,CAACiB,EAAjB;;AACA,cAAIjB,MAAM,CAAC2C,GAAP,KAAexC,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKuC,GAAL,GAAW3C,MAAM,CAAC2C,GAAlB;;AACA,cAAI3C,MAAM,CAAC4C,GAAP,KAAezC,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKwC,GAAL,GAAW5C,MAAM,CAAC4C,GAAlB;;AACA,cAAI5C,MAAM,CAAC6C,GAAP,KAAe1C,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKyC,GAAL,GAAW7C,MAAM,CAAC6C,GAAlB;;AACA,cAAI7C,MAAM,CAAC8C,EAAP,KAAc3C,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAK0C,EAAL,GAAU9C,MAAM,CAAC8C,EAAjB;;AACA,cAAI9C,MAAM,CAAC+C,EAAP,KAAc5C,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAK2C,EAAL,GAAU/C,MAAM,CAAC+C,EAAjB;;AACA,cAAI/C,MAAM,CAACgD,KAAP,KAAiB7C,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAK4C,KAAL,GAAahD,MAAM,CAACgD,KAApB;;AACA,cAAIhD,MAAM,CAACiD,GAAP,KAAe9C,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAK6C,GAAL,GAAWjD,MAAM,CAACiD,GAAlB;;AACA,cAAIjD,MAAM,CAACkD,GAAP,KAAe/C,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAK8C,GAAL,GAAWlD,MAAM,CAACkD,GAAlB;;AACA,cAAIlD,MAAM,CAACmD,EAAP,KAAchD,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAK+C,EAAL,GAAUnD,MAAM,CAACmD,EAAjB;;AACA,cAAInD,MAAM,CAACoD,GAAP,KAAejD,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKgD,GAAL,GAAWpD,MAAM,CAACoD,GAAlB;;AACA,cAAIpD,MAAM,CAACqD,EAAP,KAAclD,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKiD,EAAL,GAAUrD,MAAM,CAACqD,EAAjB;;AACA,cAAIrD,MAAM,CAACsD,EAAP,KAAcnD,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKkD,EAAL,GAAUtD,MAAM,CAACsD,EAAjB;;AACA,cAAItD,MAAM,CAACuD,EAAP,KAAcpD,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKmD,EAAL,GAAUvD,MAAM,CAACuD,EAAjB;;AACA,cAAIvD,MAAM,CAACwD,EAAP,KAAcrD,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKoD,EAAL,GAAUxD,MAAM,CAACwD,EAAjB;;AACA,cAAIxD,MAAM,CAACyD,EAAP,KAActD,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKqD,EAAL,GAAUzD,MAAM,CAACyD,EAAjB;;AACA,cAAIzD,MAAM,CAAC0D,IAAP,KAAgBvD,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKsD,IAAL,GAAY1D,MAAM,CAAC0D,IAAnB;;AACA,cAAI1D,MAAM,CAAC2D,MAAP,KAAkBxD,SAAtB,EAAiC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD,eAAKuD,MAAL,GAAc3D,MAAM,CAAC2D,MAArB;;AACA,cAAI3D,MAAM,CAAC4D,GAAP,KAAezD,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKwD,GAAL,GAAW5D,MAAM,CAAC4D,GAAlB;;AACA,cAAI5D,MAAM,CAAC6D,GAAP,KAAe1D,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKyD,GAAL,GAAW7D,MAAM,CAAC6D,GAAlB;;AACA,cAAI7D,MAAM,CAAC8D,EAAP,KAAc3D,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAK0D,EAAL,GAAU9D,MAAM,CAAC8D,EAAjB;;AACA,cAAI9D,MAAM,CAAC+D,GAAP,KAAe5D,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAK2D,GAAL,GAAW/D,MAAM,CAAC+D,GAAlB;;AACA,cAAI/D,MAAM,CAACgE,GAAP,KAAe7D,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAK4D,GAAL,GAAWhE,MAAM,CAACgE,GAAlB;;AACA,cAAIhE,MAAM,CAACiE,IAAP,KAAgB9D,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAK6D,IAAL,GAAYjE,MAAM,CAACiE,IAAnB;;AACA,cAAIjE,MAAM,CAACkE,GAAP,KAAe/D,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAK8D,GAAL,GAAWlE,MAAM,CAACkE,GAAlB;;AACA,cAAIlE,MAAM,CAACmE,EAAP,KAAchE,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAK+D,EAAL,GAAUnE,MAAM,CAACmE,EAAjB;;AACA,cAAInE,MAAM,CAACoE,EAAP,KAAcjE,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKgE,EAAL,GAAUpE,MAAM,CAACoE,EAAjB;;AACA,cAAIpE,MAAM,CAACqE,EAAP,KAAclE,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKiE,EAAL,GAAUrE,MAAM,CAACqE,EAAjB;;AACA,cAAIrE,MAAM,CAACsE,EAAP,KAAcnE,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKkE,EAAL,GAAUtE,MAAM,CAACsE,EAAjB;;AACA,cAAItE,MAAM,CAACuE,EAAP,KAAcpE,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKmE,EAAL,GAAUvE,MAAM,CAACuE,EAAjB;;AACA,cAAIvE,MAAM,CAACwE,EAAP,KAAcrE,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKoE,EAAL,GAAUxE,MAAM,CAACwE,EAAjB;;AACA,cAAIxE,MAAM,CAACyE,EAAP,KAActE,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKqE,EAAL,GAAUzE,MAAM,CAACyE,EAAjB;;AACA,cAAIzE,MAAM,CAAC0E,EAAP,KAAcvE,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKsE,EAAL,GAAU1E,MAAM,CAAC0E,EAAjB;;AACA,cAAI1E,MAAM,CAAC2E,EAAP,KAAcxE,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKuE,EAAL,GAAU3E,MAAM,CAAC2E,EAAjB;;AACA,cAAI3E,MAAM,CAAC4E,EAAP,KAAczE,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKwE,EAAL,GAAU5E,MAAM,CAAC4E,EAAjB;;AACA,cAAI5E,MAAM,CAAC6E,EAAP,KAAc1E,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKyE,EAAL,GAAU7E,MAAM,CAAC6E,EAAjB;;AACA,cAAI7E,MAAM,CAAC8E,EAAP,KAAc3E,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAK0E,EAAL,GAAU9E,MAAM,CAAC8E,EAAjB;;AACA,cAAI9E,MAAM,CAAC+E,EAAP,KAAc5E,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAK2E,EAAL,GAAU/E,MAAM,CAAC+E,EAAjB;;AACA,cAAI/E,MAAM,CAACgF,EAAP,KAAc7E,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAK4E,EAAL,GAAUhF,MAAM,CAACgF,EAAjB;;AACA,cAAIhF,MAAM,CAACiF,GAAP,KAAe9E,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAK6E,GAAL,GAAWjF,MAAM,CAACiF,GAAlB;;AACA,cAAIjF,MAAM,CAACkF,GAAP,KAAe/E,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAK8E,GAAL,GAAWlF,MAAM,CAACkF,GAAlB;;AACA,cAAIlF,MAAM,CAACmF,GAAP,KAAehF,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAK+E,GAAL,GAAWnF,MAAM,CAACmF,GAAlB;;AACA,cAAInF,MAAM,CAACoF,GAAP,KAAejF,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKgF,GAAL,GAAWpF,MAAM,CAACoF,GAAlB;;AACA,cAAIpF,MAAM,CAACqF,GAAP,KAAelF,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKiF,GAAL,GAAWrF,MAAM,CAACqF,GAAlB;;AACA,cAAIrF,MAAM,CAACsF,GAAP,KAAenF,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKkF,GAAL,GAAWtF,MAAM,CAACsF,GAAlB;;AACA,cAAItF,MAAM,CAACuF,GAAP,KAAepF,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKmF,GAAL,GAAWvF,MAAM,CAACuF,GAAlB;;AACA,cAAIvF,MAAM,CAACwF,GAAP,KAAerF,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKoF,GAAL,GAAWxF,MAAM,CAACwF,GAAlB;;AACA,cAAIxF,MAAM,CAACyF,GAAP,KAAetF,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKqF,GAAL,GAAWzF,MAAM,CAACyF,GAAlB;;AACA,cAAIzF,MAAM,CAAC0F,GAAP,KAAevF,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKsF,GAAL,GAAW1F,MAAM,CAAC0F,GAAlB;;AACA,cAAI1F,MAAM,CAAC2F,GAAP,KAAexF,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKuF,GAAL,GAAW3F,MAAM,CAAC2F,GAAlB;;AACA,cAAI3F,MAAM,CAAC4F,GAAP,KAAezF,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKwF,GAAL,GAAW5F,MAAM,CAAC4F,GAAlB;;AACA,cAAI5F,MAAM,CAAC6F,GAAP,KAAe1F,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKyF,GAAL,GAAW7F,MAAM,CAAC6F,GAAlB;;AACA,cAAI7F,MAAM,CAAC8F,IAAP,KAAgB3F,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAK0F,IAAL,GAAY9F,MAAM,CAAC8F,IAAnB;;AACA,cAAI9F,MAAM,CAAC+F,IAAP,KAAgB5F,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAK2F,IAAL,GAAY/F,MAAM,CAAC+F,IAAnB;AACH;;AA2ND1F,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAuDtB;;AAjYgB,O;;2BAwYRtE,S,GAAN,MAAMA,SAAN,CAAgB;AAEnB+D,QAAAA,WAAW,CAACC,MAAD,EAAc;AAuBzB;AACJ;AACA;AAzB6B,eA0BhBiB,EA1BgB;;AA2BzB;AACJ;AACA;AA7B6B,eA8BhB+E,QA9BgB;;AA+BzB;AACJ;AACA;AAjC6B,eAkChBC,QAlCgB;;AAmCzB;AACJ;AACA;AArC6B,eAsChBC,aAtCgB;;AAuCzB;AACJ;AACA;AAzC6B,eA0ChBC,QA1CgB;;AA2CzB;AACJ;AACA;AA7C6B,eA8ChBC,WA9CgB;;AA+CzB;AACJ;AACA;AAjD6B,eAkDhBC,KAlDgB;;AAmDzB;AACJ;AACA;AArD6B,eAsDhBC,UAtDgB;AAAA,eAuDhBC,OAvDgB;;AAwDzB;AACJ;AACA;AA1D6B,eA2DhBC,WA3DgB;;AACrB,cAAIxG,MAAM,CAACiB,EAAP,KAAcd,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKa,EAAL,GAAUjB,MAAM,CAACiB,EAAjB;;AACA,cAAIjB,MAAM,CAACgG,QAAP,KAAoB7F,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAK4F,QAAL,GAAgBhG,MAAM,CAACgG,QAAvB;;AACA,cAAIhG,MAAM,CAACiG,QAAP,KAAoB9F,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAK6F,QAAL,GAAgBjG,MAAM,CAACiG,QAAvB;;AACA,cAAIjG,MAAM,CAACkG,aAAP,KAAyB/F,SAA7B,EAAwC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC7D,eAAK8F,aAAL,GAAqBlG,MAAM,CAACkG,aAA5B;;AACA,cAAIlG,MAAM,CAACmG,QAAP,KAAoBhG,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAK+F,QAAL,GAAgBnG,MAAM,CAACmG,QAAvB;;AACA,cAAInG,MAAM,CAACoG,WAAP,KAAuBjG,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKgG,WAAL,GAAmBpG,MAAM,CAACoG,WAA1B;;AACA,cAAIpG,MAAM,CAACqG,KAAP,KAAiBlG,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAKiG,KAAL,GAAarG,MAAM,CAACqG,KAApB;;AACA,cAAIrG,MAAM,CAACsG,UAAP,KAAsBnG,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKkG,UAAL,GAAkBtG,MAAM,CAACsG,UAAzB;;AACA,cAAItG,MAAM,CAACuG,OAAP,KAAmBpG,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD;AAAE,iBAAKmG,OAAL,GAAe,EAAf;;AAAmB,iBAAI,IAAInF,KAAR,IAAiBpB,MAAM,CAACuG,OAAxB,EAAiC;AAAE,kBAAIlF,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAG,IAAI/F,WAAJ,CAAgB8F,KAAhB,CAAN;AAA8B,mBAAKmF,OAAL,CAAajF,IAAb,CAAkBD,GAAlB;AAAwB;AAAC;;AACxH,cAAIrB,MAAM,CAACwG,WAAP,KAAuBrG,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKoG,WAAL,GAAmBxG,MAAM,CAACwG,WAA1B;AACH;;AAwCDnG,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAWtB;;AA1EkB,O;;2BAiFVrE,S,GAAN,MAAMA,SAAN,CAAgB;AAEnB8D,QAAAA,WAAW,CAACC,MAAD,EAAc;AAezB;AACJ;AACA;AAjB6B,eAkBhBiB,EAlBgB;;AAmBzB;AACJ;AACA;AArB6B,eAsBhBS,IAtBgB;;AAuBzB;AACJ;AACA;AAzB6B,eA0BhB+E,MA1BgB;;AA2BzB;AACJ;AACA;AA7B6B,eA8BhBtF,IA9BgB;;AA+BzB;AACJ;AACA;AAjC6B,eAkChBuF,MAlCgB;;AAmCzB;AACJ;AACA;AArC6B,eAsChBC,iBAtCgB;;AACrB,cAAI3G,MAAM,CAACiB,EAAP,KAAcd,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKa,EAAL,GAAUjB,MAAM,CAACiB,EAAjB;;AACA,cAAIjB,MAAM,CAAC0B,IAAP,KAAgBvB,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKsB,IAAL,GAAY1B,MAAM,CAAC0B,IAAnB;;AACA,cAAI1B,MAAM,CAACyG,MAAP,KAAkBtG,SAAtB,EAAiC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD,eAAKqG,MAAL,GAAczG,MAAM,CAACyG,MAArB;;AACA,cAAIzG,MAAM,CAACmB,IAAP,KAAgBhB,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKe,IAAL,GAAYnB,MAAM,CAACmB,IAAnB;;AACA,cAAInB,MAAM,CAAC0G,MAAP,KAAkBvG,SAAtB,EAAiC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD,eAAKsG,MAAL,GAAc1G,MAAM,CAAC0G,MAArB;;AACA,cAAI1G,MAAM,CAAC4G,kBAAP,KAA8BzG,SAAlC,EAA6C;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClE,eAAKuG,iBAAL,GAAyB3G,MAAM,CAAC4G,kBAAhC;AACH;;AA2BDvG,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAOtB;;AAjDkB,O;;4BAwDVpE,U,GAAN,MAAMA,UAAN,CAAiB;AAEpB6D,QAAAA,WAAW,CAACC,MAAD,EAAc;AAiBzB;AACJ;AACA;AAnB6B,eAoBhBiB,EApBgB;;AAqBzB;AACJ;AACA;AAvB6B,eAwBhB4F,UAxBgB;;AAyBzB;AACJ;AACA;AA3B6B,eA4BhBC,eA5BgB;;AA6BzB;AACJ;AACA;AA/B6B,eAgChBC,QAhCgB;;AAiCzB;AACJ;AACA;AAnC6B,eAoChBC,WApCgB;;AAqCzB;AACJ;AACA;AAvC6B,eAwChBC,UAxCgB;AAAA,eAyChBC,YAzCgB;;AACrB,cAAIlH,MAAM,CAACiB,EAAP,KAAcd,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKa,EAAL,GAAUjB,MAAM,CAACiB,EAAjB;;AACA,cAAIjB,MAAM,CAAC6G,UAAP,KAAsB1G,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKyG,UAAL,GAAkB7G,MAAM,CAAC6G,UAAzB;;AACA,cAAI7G,MAAM,CAAC8G,eAAP,KAA2B3G,SAA/B,EAA0C;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC/D,eAAK0G,eAAL,GAAuB9G,MAAM,CAAC8G,eAA9B;;AACA,cAAI9G,MAAM,CAAC+G,QAAP,KAAoB5G,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAK2G,QAAL,GAAgB/G,MAAM,CAAC+G,QAAvB;;AACA,cAAI/G,MAAM,CAACgH,WAAP,KAAuB7G,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAK4G,WAAL,GAAmBhH,MAAM,CAACgH,WAA1B;;AACA,cAAIhH,MAAM,CAACiH,UAAP,KAAsB9G,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAK6G,UAAL,GAAkBjH,MAAM,CAACiH,UAAzB;;AACA,cAAIjH,MAAM,CAACkH,YAAP,KAAwB/G,SAA5B,EAAuC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC5D;AAAE,iBAAK8G,YAAL,GAAoB,EAApB;;AAAwB,iBAAI,IAAI9F,KAAR,IAAiBpB,MAAM,CAACkH,YAAxB,EAAsC;AAAE,kBAAI7F,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAG,IAAIxF,YAAJ,CAAiBuF,KAAjB,CAAN;AAA+B,mBAAK8F,YAAL,CAAkB5F,IAAlB,CAAuBD,GAAvB;AAA6B;AAAC;AAC3I;;AA4BDhB,QAAAA,OAAO,CAACC,MAAD,EAAgB;AAOnB,eAAK,IAAI6G,EAAT,IAAe,KAAKD,YAApB,EAAkC;AAAEC,YAAAA,EAAE,QAAF,IAAAA,EAAE,CAAE9G,OAAJ,CAAYC,MAAZ;AAAsB;AAC7D;;AArDmB,O;;2BA4DXnE,S,GAAN,MAAMA,SAAN,CAAgB;AAEnB4D,QAAAA,WAAW,CAACC,MAAD,EAAc;AAiBzB;AACJ;AACA;AAnB6B,eAoBhBiB,EApBgB;;AAqBzB;AACJ;AACA;AAvB6B,eAwBhBS,IAxBgB;;AAyBzB;AACJ;AACA;AA3B6B,eA4BhB0F,WA5BgB;;AA6BzB;AACJ;AACA;AA/B6B,eAgChBC,IAhCgB;;AAiCzB;AACJ;AACA;AAnC6B,eAoChBC,UApCgB;;AAqCzB;AACJ;AACA;AAvC6B,eAwChBC,WAxCgB;;AAyCzB;AACJ;AACA;AA3C6B,eA4ChBC,YA5CgB;;AACrB,cAAIxH,MAAM,CAACiB,EAAP,KAAcd,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKa,EAAL,GAAUjB,MAAM,CAACiB,EAAjB;;AACA,cAAIjB,MAAM,CAAC0B,IAAP,KAAgBvB,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKsB,IAAL,GAAY1B,MAAM,CAAC0B,IAAnB;;AACA,cAAI1B,MAAM,CAACoH,WAAP,KAAuBjH,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKgH,WAAL,GAAmBpH,MAAM,CAACoH,WAA1B;;AACA,cAAIpH,MAAM,CAACqH,IAAP,KAAgBlH,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKiH,IAAL,GAAYrH,MAAM,CAACqH,IAAnB;;AACA,cAAIrH,MAAM,CAACyH,WAAP,KAAuBtH,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKkH,UAAL,GAAkBtH,MAAM,CAACyH,WAAzB;;AACA,cAAIzH,MAAM,CAAC0H,YAAP,KAAwBvH,SAA5B,EAAuC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC5D,eAAKmH,WAAL,GAAmBvH,MAAM,CAAC0H,YAA1B;;AACA,cAAI1H,MAAM,CAAC2H,aAAP,KAAyBxH,SAA7B,EAAwC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC7D,eAAKoH,YAAL,GAAoBxH,MAAM,CAAC2H,aAA3B;AACH;;AA+BDtH,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAQtB;;AAxDkB,O;;0BA+DVlE,Q,GAAN,MAAMA,QAAN,CAAe;AAElB2D,QAAAA,WAAW,CAACC,MAAD,EAAc;AAqCzB;AACJ;AACA;AAvC6B,eAwChBiB,EAxCgB;;AAyCzB;AACJ;AACA;AA3C6B,eA4ChB2G,IA5CgB;;AA6CzB;AACJ;AACA;AA/C6B,eAgDhB7D,GAhDgB;;AAiDzB;AACJ;AACA;AAnD6B,eAoDhB8D,EApDgB;;AAqDzB;AACJ;AACA;AAvD6B,eAwDhBC,YAxDgB;;AAyDzB;AACJ;AACA;AA3D6B,eA4DhBC,IA5DgB;;AA6DzB;AACJ;AACA;AA/D6B,eAgEhBC,MAhEgB;;AAiEzB;AACJ;AACA;AAnE6B,eAoEhBC,aApEgB;;AAqEzB;AACJ;AACA;AAvE6B,eAwEhBC,YAxEgB;;AAyEzB;AACJ;AACA;AA3E6B,eA4EhBC,WA5EgB;;AA6EzB;AACJ;AACA;AA/E6B,eAgFhBC,WAhFgB;;AAiFzB;AACJ;AACA;AAnF6B,eAoFhBC,cApFgB;;AAqFzB;AACJ;AACA;AAvF6B,eAwFhBC,SAxFgB;;AAyFzB;AACJ;AACA;AA3F6B,eA4FhBC,UA5FgB;;AA6FzB;AACJ;AACA;AA/F6B,eAgGhBvH,KAhGgB;;AAiGzB;AACJ;AACA;AAnG6B,eAoGhBwH,QApGgB;;AAqGzB;AACJ;AACA;AAvG6B,eAwGhBC,SAxGgB;;AACrB,cAAIzI,MAAM,CAACiB,EAAP,KAAcd,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKa,EAAL,GAAUjB,MAAM,CAACiB,EAAjB;;AACA,cAAIjB,MAAM,CAAC4H,IAAP,KAAgBzH,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKwH,IAAL,GAAY5H,MAAM,CAAC4H,IAAnB;;AACA,cAAI5H,MAAM,CAAC+D,GAAP,KAAe5D,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAK2D,GAAL,GAAW/D,MAAM,CAAC+D,GAAlB;;AACA,cAAI/D,MAAM,CAAC6H,EAAP,KAAc1H,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKyH,EAAL,GAAU7H,MAAM,CAAC6H,EAAjB;;AACA,cAAI7H,MAAM,CAAC8H,YAAP,KAAwB3H,SAA5B,EAAuC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC5D,eAAK0H,YAAL,GAAoB9H,MAAM,CAAC8H,YAA3B;;AACA,cAAI9H,MAAM,CAAC+H,IAAP,KAAgB5H,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAK2H,IAAL,GAAY/H,MAAM,CAAC+H,IAAnB;;AACA,cAAI/H,MAAM,CAACgI,MAAP,KAAkB7H,SAAtB,EAAiC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD,eAAK4H,MAAL,GAAchI,MAAM,CAACgI,MAArB;;AACA,cAAIhI,MAAM,CAACiI,aAAP,KAAyB9H,SAA7B,EAAwC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC7D,eAAK6H,aAAL,GAAqBjI,MAAM,CAACiI,aAA5B;;AACA,cAAIjI,MAAM,CAACkI,YAAP,KAAwB/H,SAA5B,EAAuC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC5D,eAAK8H,YAAL,GAAoBlI,MAAM,CAACkI,YAA3B;;AACA,cAAIlI,MAAM,CAACmI,WAAP,KAAuBhI,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAK+H,WAAL,GAAmBnI,MAAM,CAACmI,WAA1B;;AACA,cAAInI,MAAM,CAACoI,WAAP,KAAuBjI,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKgI,WAAL,GAAmBpI,MAAM,CAACoI,WAA1B;;AACA,cAAIpI,MAAM,CAACqI,cAAP,KAA0BlI,SAA9B,EAAyC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC9D,eAAKiI,cAAL,GAAsBrI,MAAM,CAACqI,cAA7B;;AACA,cAAIrI,MAAM,CAACsI,SAAP,KAAqBnI,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKkI,SAAL,GAAiBtI,MAAM,CAACsI,SAAxB;;AACA,cAAItI,MAAM,CAACuI,UAAP,KAAsBpI,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKmI,UAAL,GAAkBvI,MAAM,CAACuI,UAAzB;;AACA,cAAIvI,MAAM,CAACgB,KAAP,KAAiBb,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAKY,KAAL,GAAahB,MAAM,CAACgB,KAApB;;AACA,cAAIhB,MAAM,CAACwI,QAAP,KAAoBrI,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAKoI,QAAL,GAAgBxI,MAAM,CAACwI,QAAvB;;AACA,cAAIxI,MAAM,CAACyI,SAAP,KAAqBtI,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKqI,SAAL,GAAiBzI,MAAM,CAACyI,SAAxB;AACH;;AAuEDpI,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAkBtB;;AA9HiB,O;;0BAqITjE,Q,GAAN,MAAMA,QAAN,CAAe;AAElB0D,QAAAA,WAAW,CAACC,MAAD,EAAc;AAmBzB;AACJ;AACA;AArB6B,eAsBhBiB,EAtBgB;;AAuBzB;AACJ;AACA;AAzB6B,eA0BhBS,IA1BgB;;AA2BzB;AACJ;AACA;AA7B6B,eA8BhB2F,IA9BgB;;AA+BzB;AACJ;AACA;AAjC6B,eAkChBqB,OAlCgB;;AAmCzB;AACJ;AACA;AArC6B,eAsChBC,UAtCgB;;AAuCzB;AACJ;AACA;AAzC6B,eA0ChBC,UA1CgB;;AA2CzB;AACJ;AACA;AA7C6B,eA8ChBC,KA9CgB;;AA+CzB;AACJ;AACA;AAjD6B,eAkDhBC,YAlDgB;;AACrB,cAAI9I,MAAM,CAACiB,EAAP,KAAcd,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKa,EAAL,GAAUjB,MAAM,CAACiB,EAAjB;;AACA,cAAIjB,MAAM,CAAC0B,IAAP,KAAgBvB,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKsB,IAAL,GAAY1B,MAAM,CAAC0B,IAAnB;;AACA,cAAI1B,MAAM,CAACqH,IAAP,KAAgBlH,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKiH,IAAL,GAAYrH,MAAM,CAACqH,IAAnB;;AACA,cAAIrH,MAAM,CAAC0I,OAAP,KAAmBvI,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAKsI,OAAL,GAAe1I,MAAM,CAAC0I,OAAtB;;AACA,cAAI1I,MAAM,CAAC+I,WAAP,KAAuB5I,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKuI,UAAL,GAAkB3I,MAAM,CAAC+I,WAAzB;;AACA,cAAI/I,MAAM,CAACgJ,WAAP,KAAuB7I,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKwI,UAAL,GAAkB5I,MAAM,CAACgJ,WAAzB;;AACA,cAAIhJ,MAAM,CAAC6I,KAAP,KAAiB1I,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD;AAAE,iBAAKyI,KAAL,GAAa,EAAb;;AAAiB,iBAAI,IAAIzH,KAAR,IAAiBpB,MAAM,CAAC6I,KAAxB,EAA+B;AAAE,kBAAIxH,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAG,IAAI9F,SAAJ,CAAc6F,KAAd,CAAN;AAA4B,mBAAKyH,KAAL,CAAWvH,IAAX,CAAgBD,GAAhB;AAAsB;AAAC;;AAChH,cAAIrB,MAAM,CAACiJ,aAAP,KAAyB9I,SAA7B,EAAwC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC7D;AAAE,iBAAK0I,YAAL,GAAoB,EAApB;;AAAwB,iBAAI,IAAI1H,IAAR,IAAiBpB,MAAM,CAACiJ,aAAxB,EAAuC;AAAE,kBAAI5H,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAG,IAAIjG,WAAJ,CAAgBgG,IAAhB,CAAN;AAA8B,mBAAK0H,YAAL,CAAkBxH,IAAlB,CAAuBD,GAAvB;AAA6B;AAAC;AAC3I;;AAmCDhB,QAAAA,OAAO,CAACC,MAAD,EAAgB;AAOnB,eAAK,IAAI6G,EAAT,IAAe,KAAK0B,KAApB,EAA2B;AAAE1B,YAAAA,EAAE,QAAF,IAAAA,EAAE,CAAE9G,OAAJ,CAAYC,MAAZ;AAAsB;;AACnD,eAAK,IAAI6G,GAAT,IAAe,KAAK2B,YAApB,EAAkC;AAAE3B,YAAAA,GAAE,QAAF,IAAAA,GAAE,CAAE9G,OAAJ,CAAYC,MAAZ;AAAsB;AAC7D;;AA/DiB,O;;iCAsEThE,e,GAAN,MAAMA,eAAN,CAAsB;AAEzByD,QAAAA,WAAW,CAACC,MAAD,EAAc;AAezB;AACJ;AACA;AAjB6B,eAkBhB4I,UAlBgB;;AAmBzB;AACJ;AACA;AArB6B,eAsBhBM,SAtBgB;;AAuBzB;AACJ;AACA;AAzB6B,eA0BhBC,OA1BgB;;AA2BzB;AACJ;AACA;AA7B6B,eA8BhBC,OA9BgB;;AA+BzB;AACJ;AACA;AAjC6B,eAkChBC,YAlCgB;;AAmCzB;AACJ;AACA;AArC6B,eAsChBP,YAtCgB;;AACrB,cAAI9I,MAAM,CAACgJ,WAAP,KAAuB7I,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKwI,UAAL,GAAkB5I,MAAM,CAACgJ,WAAzB;;AACA,cAAIhJ,MAAM,CAACsJ,UAAP,KAAsBnJ,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAK8I,SAAL,GAAiBlJ,MAAM,CAACsJ,UAAxB;;AACA,cAAItJ,MAAM,CAACuJ,QAAP,KAAoBpJ,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAK+I,OAAL,GAAenJ,MAAM,CAACuJ,QAAtB;;AACA,cAAIvJ,MAAM,CAACwJ,QAAP,KAAoBrJ,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD;AAAE,iBAAKgJ,OAAL,GAAe,EAAf;;AAAmB,iBAAI,IAAIhI,KAAR,IAAiBpB,MAAM,CAACwJ,QAAxB,EAAkC;AAAE,kBAAInI,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAG,IAAIzF,OAAJ,CAAYwF,KAAZ,CAAN;AAA0B,mBAAKgI,OAAL,CAAa9H,IAAb,CAAkBD,GAAlB;AAAwB;AAAC;;AACrH,cAAIrB,MAAM,CAACyJ,aAAP,KAAyBtJ,SAA7B,EAAwC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC7D,eAAKiJ,YAAL,GAAoB,IAAIhO,YAAJ,CAAiB2E,MAAM,CAACyJ,aAAxB,CAApB;;AACA,cAAIzJ,MAAM,CAACiJ,aAAP,KAAyB9I,SAA7B,EAAwC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC7D;AAAE,iBAAK0I,YAAL,GAAoB,EAApB;;AAAwB,iBAAI,IAAI1H,KAAR,IAAiBpB,MAAM,CAACiJ,aAAxB,EAAuC;AAAE,kBAAI5H,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAG,IAAIjG,WAAJ,CAAgBgG,KAAhB,CAAN;AAA8B,mBAAK0H,YAAL,CAAkBxH,IAAlB,CAAuBD,GAAvB;AAA6B;AAAC;AAC3I;;AA2BDhB,QAAAA,OAAO,CAACC,MAAD,EAAgB;AAAA;;AAInB,eAAK,IAAI6G,EAAT,IAAe,KAAKiC,OAApB,EAA6B;AAAEjC,YAAAA,EAAE,QAAF,IAAAA,EAAE,CAAE9G,OAAJ,CAAYC,MAAZ;AAAsB;;AACrD,qCAAK+I,YAAL,gCAAmBhJ,OAAnB,CAA2BC,MAA3B;;AACA,eAAK,IAAI6G,GAAT,IAAe,KAAK2B,YAApB,EAAkC;AAAE3B,YAAAA,GAAE,QAAF,IAAAA,GAAE,CAAE9G,OAAJ,CAAYC,MAAZ;AAAsB;AAC7D;;AAjDwB,O;;6BAwDhB/D,W,GAAN,MAAMA,WAAN,CAAkB;AAErBwD,QAAAA,WAAW,CAACC,MAAD,EAAc;AA+CzB;AACJ;AACA;AAjD6B,eAkDhBwC,EAlDgB;;AAmDzB;AACJ;AACA;AArD6B,eAsDhBkH,QAtDgB;;AAuDzB;AACJ;AACA;AAzD6B,eA0DhBC,SA1DgB;;AA2DzB;AACJ;AACA;AA7D6B,eA8DhBC,KA9DgB;;AA+DzB;AACJ;AACA;AAjE6B,eAkEhBC,UAlEgB;;AAmEzB;AACJ;AACA;AArE6B,eAsEhBzC,WAtEgB;AAAA,eAuEhB0C,OAvEgB;;AAwEzB;AACJ;AACA;AA1E6B,eA2EhBzD,KA3EgB;;AA4EzB;AACJ;AACA;AA9E6B,eA+EhB0D,KA/EgB;;AAgFzB;AACJ;AACA;AAlF6B,eAmFhBC,OAnFgB;;AAoFzB;AACJ;AACA;AAtF6B,eAuFhBC,UAvFgB;;AAwFzB;AACJ;AACA;AA1F6B,eA2FhBC,UA3FgB;;AA4FzB;AACJ;AACA;AA9F6B,eA+FhBC,YA/FgB;;AAgGzB;AACJ;AACA;AAlG6B,eAmGhBC,WAnGgB;;AAoGzB;AACJ;AACA;AAtG6B,eAuGhBC,KAvGgB;;AAwGzB;AACJ;AACA;AA1G6B,eA2GhBC,OA3GgB;;AA4GzB;AACJ;AACA;AA9G6B,eA+GhBC,UA/GgB;;AAgHzB;AACJ;AACA;AAlH6B,eAmHhBC,UAnHgB;;AAoHzB;AACJ;AACA;AAtH6B,eAuHhBC,UAvHgB;;AAwHzB;AACJ;AACA;AA1H6B,eA2HhBC,SA3HgB;;AA4HzB;AACJ;AACA;AA9H6B,eA+HhBC,SA/HgB;AAAA,eAgIhBC,UAhIgB;;AACrB,cAAI5K,MAAM,CAACwC,EAAP,KAAcrC,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKoC,EAAL,GAAUxC,MAAM,CAACwC,EAAjB;;AACA,cAAIxC,MAAM,CAAC0J,QAAP,KAAoBvJ,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAKsJ,QAAL,GAAgB1J,MAAM,CAAC0J,QAAvB;;AACA,cAAI1J,MAAM,CAAC2J,SAAP,KAAqBxJ,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKuJ,SAAL,GAAiB3J,MAAM,CAAC2J,SAAxB;;AACA,cAAI3J,MAAM,CAAC4J,KAAP,KAAiBzJ,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAKwJ,KAAL,GAAa5J,MAAM,CAAC4J,KAApB;;AACA,cAAI5J,MAAM,CAAC6J,UAAP,KAAsB1J,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKyJ,UAAL,GAAkB7J,MAAM,CAAC6J,UAAzB;;AACA,cAAI7J,MAAM,CAACoH,WAAP,KAAuBjH,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKgH,WAAL,GAAmBpH,MAAM,CAACoH,WAA1B;;AACA,cAAIpH,MAAM,CAAC8J,OAAP,KAAmB3J,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD;AAAE,iBAAK0J,OAAL,GAAe,EAAf;;AAAmB,iBAAI,IAAI1I,KAAR,IAAiBpB,MAAM,CAAC8J,OAAxB,EAAiC;AAAE,kBAAIzI,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAG,IAAIlG,QAAJ,CAAaiG,KAAb,CAAN;AAA2B,mBAAK0I,OAAL,CAAaxI,IAAb,CAAkBD,GAAlB;AAAwB;AAAC;;AACrH,cAAIrB,MAAM,CAACqG,KAAP,KAAiBlG,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAKiG,KAAL,GAAarG,MAAM,CAACqG,KAApB;;AACA,cAAIrG,MAAM,CAAC+J,KAAP,KAAiB5J,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAK2J,KAAL,GAAa/J,MAAM,CAAC+J,KAApB;;AACA,cAAI/J,MAAM,CAACgK,OAAP,KAAmB7J,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAK4J,OAAL,GAAehK,MAAM,CAACgK,OAAtB;;AACA,cAAIhK,MAAM,CAACiK,UAAP,KAAsB9J,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAK6J,UAAL,GAAkBjK,MAAM,CAACiK,UAAzB;;AACA,cAAIjK,MAAM,CAACkK,UAAP,KAAsB/J,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAK8J,UAAL,GAAkBlK,MAAM,CAACkK,UAAzB;;AACA,cAAIlK,MAAM,CAACmK,YAAP,KAAwBhK,SAA5B,EAAuC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC5D,eAAK+J,YAAL,GAAoBnK,MAAM,CAACmK,YAA3B;;AACA,cAAInK,MAAM,CAACoK,WAAP,KAAuBjK,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKgK,WAAL,GAAmBpK,MAAM,CAACoK,WAA1B;;AACA,cAAIpK,MAAM,CAACqK,KAAP,KAAiBlK,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAKiK,KAAL,GAAarK,MAAM,CAACqK,KAApB;;AACA,cAAIrK,MAAM,CAACsK,OAAP,KAAmBnK,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAKkK,OAAL,GAAetK,MAAM,CAACsK,OAAtB;;AACA,cAAItK,MAAM,CAACuK,UAAP,KAAsBpK,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKmK,UAAL,GAAkBvK,MAAM,CAACuK,UAAzB;;AACA,cAAIvK,MAAM,CAACwK,UAAP,KAAsBrK,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKoK,UAAL,GAAkBxK,MAAM,CAACwK,UAAzB;;AACA,cAAIxK,MAAM,CAACyK,UAAP,KAAsBtK,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKqK,UAAL,GAAkBzK,MAAM,CAACyK,UAAzB;;AACA,cAAIzK,MAAM,CAAC0K,SAAP,KAAqBvK,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKsK,SAAL,GAAiB1K,MAAM,CAAC0K,SAAxB;;AACA,cAAI1K,MAAM,CAAC2K,SAAP,KAAqBxK,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKuK,SAAL,GAAiB3K,MAAM,CAAC2K,SAAxB;;AACA,cAAI3K,MAAM,CAAC4K,UAAP,KAAsBzK,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D;AAAE,iBAAKwK,UAAL,GAAkB,EAAlB;;AAAsB,iBAAI,IAAIxJ,KAAR,IAAiBpB,MAAM,CAAC4K,UAAxB,EAAoC;AAAE,kBAAIvJ,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAG,IAAIvF,WAAJ,CAAgBsF,KAAhB,CAAN;AAA8B,mBAAKwJ,UAAL,CAAgBtJ,IAAhB,CAAqBD,GAArB;AAA2B;AAAC;AACpI;;AAqFDhB,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAuBtB;;AA3JoB,O;;+BAkKZ9D,a,GAAN,MAAMA,aAAN,CAAoB;AAEvBuD,QAAAA,WAAW,CAACC,MAAD,EAAc;AAezB;AACJ;AACA;AAjB6B,eAkBhB6K,aAlBgB;;AAmBzB;AACJ;AACA;AArB6B,eAsBhBC,SAtBgB;;AAuBzB;AACJ;AACA;AAzB6B,eA0BhBC,qBA1BgB;;AA2BzB;AACJ;AACA;AA7B6B,eA8BhBC,kBA9BgB;;AA+BzB;AACJ;AACA;AAjC6B,eAkChBC,gBAlCgB;;AAmCzB;AACJ;AACA;AArC6B,eAsChBC,iBAtCgB;;AACrB,cAAIlL,MAAM,CAAC6K,aAAP,KAAyB1K,SAA7B,EAAwC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC7D,eAAKyK,aAAL,GAAqB7K,MAAM,CAAC6K,aAA5B;;AACA,cAAI7K,MAAM,CAAC8K,SAAP,KAAqB3K,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAK0K,SAAL,GAAiB9K,MAAM,CAAC8K,SAAxB;;AACA,cAAI9K,MAAM,CAAC+K,qBAAP,KAAiC5K,SAArC,EAAgD;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrE,eAAK2K,qBAAL,GAA6B/K,MAAM,CAAC+K,qBAApC;;AACA,cAAI/K,MAAM,CAACgL,kBAAP,KAA8B7K,SAAlC,EAA6C;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClE,eAAK4K,kBAAL,GAA0BhL,MAAM,CAACgL,kBAAjC;;AACA,cAAIhL,MAAM,CAACiL,gBAAP,KAA4B9K,SAAhC,EAA2C;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAChE,eAAK6K,gBAAL,GAAwBjL,MAAM,CAACiL,gBAA/B;;AACA,cAAIjL,MAAM,CAACkL,iBAAP,KAA6B/K,SAAjC,EAA4C;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACjE,eAAK8K,iBAAL,GAAyBlL,MAAM,CAACkL,iBAAhC;AACH;;AA2BD7K,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAOtB;;AAjDsB,O;;yBAwDd7D,O,GAAN,MAAMA,OAAN,CAAc;AAEjBsD,QAAAA,WAAW,CAACC,MAAD,EAAc;AAuBzB;AACJ;AACA;AAzB6B,eA0BhBiB,EA1BgB;;AA2BzB;AACJ;AACA;AA7B6B,eA8BhBS,IA9BgB;;AA+BzB;AACJ;AACA;AAjC6B,eAkChB2F,IAlCgB;;AAmCzB;AACJ;AACA;AArC6B,eAsChBqB,OAtCgB;;AAuCzB;AACJ;AACA;AAzC6B,eA0ChBC,UA1CgB;;AA2CzB;AACJ;AACA;AA7C6B,eA8ChBwC,OA9CgB;;AA+CzB;AACJ;AACA;AAjD6B,eAkDhBtJ,QAlDgB;;AAmDzB;AACJ;AACA;AArD6B,eAsDhBuJ,YAtDgB;;AAuDzB;AACJ;AACA;AAzD6B,eA0DhBC,YA1DgB;;AA2DzB;AACJ;AACA;AA7D6B,eA8DhBC,WA9DgB;;AACrB,cAAItL,MAAM,CAACiB,EAAP,KAAcd,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKa,EAAL,GAAUjB,MAAM,CAACiB,EAAjB;;AACA,cAAIjB,MAAM,CAAC0B,IAAP,KAAgBvB,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKsB,IAAL,GAAY1B,MAAM,CAAC0B,IAAnB;;AACA,cAAI1B,MAAM,CAACqH,IAAP,KAAgBlH,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKiH,IAAL,GAAYrH,MAAM,CAACqH,IAAnB;;AACA,cAAIrH,MAAM,CAAC0I,OAAP,KAAmBvI,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAKsI,OAAL,GAAe1I,MAAM,CAAC0I,OAAtB;;AACA,cAAI1I,MAAM,CAAC+I,WAAP,KAAuB5I,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKuI,UAAL,GAAkB3I,MAAM,CAAC+I,WAAzB;;AACA,cAAI/I,MAAM,CAACuL,QAAP,KAAoBpL,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAK+K,OAAL,GAAenL,MAAM,CAACuL,QAAtB;;AACA,cAAIvL,MAAM,CAAC8B,SAAP,KAAqB3B,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKyB,QAAL,GAAgB7B,MAAM,CAAC8B,SAAvB;;AACA,cAAI9B,MAAM,CAACwL,aAAP,KAAyBrL,SAA7B,EAAwC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC7D,eAAKgL,YAAL,GAAoBpL,MAAM,CAACwL,aAA3B;;AACA,cAAIxL,MAAM,CAACyL,aAAP,KAAyBtL,SAA7B,EAAwC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC7D,eAAKiL,YAAL,GAAoBrL,MAAM,CAACyL,aAA3B;;AACA,cAAIzL,MAAM,CAAC0L,aAAP,KAAyBvL,SAA7B,EAAwC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC7D,eAAKkL,WAAL,GAAmBtL,MAAM,CAAC0L,aAA1B;AACH;;AA2CDrL,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAWtB;;AA7EgB,O;;0BAoFR5D,Q,GAAN,MAAMA,QAAN,CAAe;AAElBqD,QAAAA,WAAW,CAACC,MAAD,EAAc;AAiBzB;AACJ;AACA;AAnB6B,eAoBhBiB,EApBgB;;AAqBzB;AACJ;AACA;AAvB6B,eAwBhByF,MAxBgB;;AAyBzB;AACJ;AACA;AA3B6B,eA4BhBiF,UA5BgB;;AA6BzB;AACJ;AACA;AA/B6B,eAgChBC,WAhCgB;;AAiCzB;AACJ;AACA;AAnC6B,eAoChBC,cApCgB;;AAqCzB;AACJ;AACA;AAvC6B,eAwChBC,qBAxCgB;;AAyCzB;AACJ;AACA;AA3C6B,eA4ChBC,SA5CgB;;AACrB,cAAI/L,MAAM,CAACiB,EAAP,KAAcd,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKa,EAAL,GAAUjB,MAAM,CAACiB,EAAjB;;AACA,cAAIjB,MAAM,CAAC0G,MAAP,KAAkBvG,SAAtB,EAAiC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD,eAAKsG,MAAL,GAAc1G,MAAM,CAAC0G,MAArB;;AACA,cAAI1G,MAAM,CAAC2L,UAAP,KAAsBxL,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKuL,UAAL,GAAkB3L,MAAM,CAAC2L,UAAzB;;AACA,cAAI3L,MAAM,CAAC4L,WAAP,KAAuBzL,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKwL,WAAL,GAAmB5L,MAAM,CAAC4L,WAA1B;;AACA,cAAI5L,MAAM,CAAC6L,cAAP,KAA0B1L,SAA9B,EAAyC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC9D,eAAKyL,cAAL,GAAsB7L,MAAM,CAAC6L,cAA7B;;AACA,cAAI7L,MAAM,CAAC8L,qBAAP,KAAiC3L,SAArC,EAAgD;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrE,eAAK0L,qBAAL,GAA6B9L,MAAM,CAAC8L,qBAApC;;AACA,cAAI9L,MAAM,CAAC+L,SAAP,KAAqB5L,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAK2L,SAAL,GAAiB/L,MAAM,CAAC+L,SAAxB;AACH;;AA+BD1L,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAQtB;;AAxDiB,O;;+BA+DT3D,a,GAAN,MAAMA,aAAN,CAAoB;AAEvBoD,QAAAA,WAAW,CAACC,MAAD,EAAc;AAiBzB;AACJ;AACA;AAnB6B,eAoBhBiB,EApBgB;;AAqBzB;AACJ;AACA;AAvB6B,eAwBhB+K,cAxBgB;;AAyBzB;AACJ;AACA;AA3B6B,eA4BhBC,WA5BgB;AAAA,eA6BhBC,UA7BgB;;AA8BzB;AACJ;AACA;AAhC6B,eAiChBC,cAjCgB;;AAkCzB;AACJ;AACA;AApC6B,eAqChBC,WArCgB;AAAA,eAsChBC,UAtCgB;;AACrB,cAAIrM,MAAM,CAACiB,EAAP,KAAcd,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKa,EAAL,GAAUjB,MAAM,CAACiB,EAAjB;;AACA,cAAIjB,MAAM,CAACgM,cAAP,KAA0B7L,SAA9B,EAAyC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC9D,eAAK4L,cAAL,GAAsBhM,MAAM,CAACgM,cAA7B;;AACA,cAAIhM,MAAM,CAACiM,WAAP,KAAuB9L,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAK6L,WAAL,GAAmBjM,MAAM,CAACiM,WAA1B;;AACA,cAAIjM,MAAM,CAACkM,UAAP,KAAsB/L,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D;AAAE,iBAAK8L,UAAL,GAAkB,EAAlB;;AAAsB,iBAAI,IAAI9K,KAAR,IAAiBpB,MAAM,CAACkM,UAAxB,EAAoC;AAAE,kBAAI7K,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAG,IAAIxF,YAAJ,CAAiBuF,KAAjB,CAAN;AAA+B,mBAAK8K,UAAL,CAAgB5K,IAAhB,CAAqBD,GAArB;AAA2B;AAAC;;AAClI,cAAIrB,MAAM,CAACmM,cAAP,KAA0BhM,SAA9B,EAAyC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC9D,eAAK+L,cAAL,GAAsBnM,MAAM,CAACmM,cAA7B;;AACA,cAAInM,MAAM,CAACoM,WAAP,KAAuBjM,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKgM,WAAL,GAAmBpM,MAAM,CAACoM,WAA1B;;AACA,cAAIpM,MAAM,CAACqM,UAAP,KAAsBlM,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D;AAAE,iBAAKiM,UAAL,GAAkB,EAAlB;;AAAsB,iBAAI,IAAIjL,KAAR,IAAiBpB,MAAM,CAACqM,UAAxB,EAAoC;AAAE,kBAAIhL,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAG,IAAIxF,YAAJ,CAAiBuF,KAAjB,CAAN;AAA+B,mBAAKiL,UAAL,CAAgB/K,IAAhB,CAAqBD,GAArB;AAA2B;AAAC;AACrI;;AAyBDhB,QAAAA,OAAO,CAACC,MAAD,EAAgB;AAInB,eAAK,IAAI6G,EAAT,IAAe,KAAK+E,UAApB,EAAgC;AAAE/E,YAAAA,EAAE,QAAF,IAAAA,EAAE,CAAE9G,OAAJ,CAAYC,MAAZ;AAAsB;;AAGxD,eAAK,IAAI6G,GAAT,IAAe,KAAKkF,UAApB,EAAgC;AAAElF,YAAAA,GAAE,QAAF,IAAAA,GAAE,CAAE9G,OAAJ,CAAYC,MAAZ;AAAsB;AAC3D;;AAlDsB,O;;8BAyDd1D,Y,GAAN,MAAMA,YAAN,CAAmB;AAEtBmD,QAAAA,WAAW,CAACC,MAAD,EAAc;AAmBzB;AACJ;AACA;AArB6B,eAsBhBiB,EAtBgB;;AAuBzB;AACJ;AACA;AAzB6B,eA0BhBE,IA1BgB;;AA2BzB;AACJ;AACA;AA7B6B,eA8BhBmL,IA9BgB;;AA+BzB;AACJ;AACA;AAjC6B,eAkChBC,QAlCgB;;AAmCzB;AACJ;AACA;AArC6B,eAsChBC,QAtCgB;;AAuCzB;AACJ;AACA;AAzC6B,eA0ChBC,QA1CgB;;AA2CzB;AACJ;AACA;AA7C6B,eA8ChBC,SA9CgB;;AA+CzB;AACJ;AACA;AAjD6B,eAkDhBC,YAlDgB;;AACrB,cAAI3M,MAAM,CAACiB,EAAP,KAAcd,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKa,EAAL,GAAUjB,MAAM,CAACiB,EAAjB;;AACA,cAAIjB,MAAM,CAACmB,IAAP,KAAgBhB,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKe,IAAL,GAAYnB,MAAM,CAACmB,IAAnB;;AACA,cAAInB,MAAM,CAACsM,IAAP,KAAgBnM,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD;AAAE,iBAAKkM,IAAL,GAAY,EAAZ;;AAAgB,iBAAI,IAAIlL,KAAR,IAAiBpB,MAAM,CAACsM,IAAxB,EAA8B;AAAE,kBAAIjL,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAKkL,IAAL,CAAUhL,IAAV,CAAeD,GAAf;AAAqB;AAAC;;AAC9F,cAAIrB,MAAM,CAACuM,QAAP,KAAoBpM,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD;AAAE,iBAAKmM,QAAL,GAAgB,EAAhB;;AAAoB,iBAAI,IAAInL,KAAR,IAAiBpB,MAAM,CAACuM,QAAxB,EAAkC;AAAE,kBAAIlL,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAKmL,QAAL,CAAcjL,IAAd,CAAmBD,GAAnB;AAAyB;AAAC;;AAC1G,cAAIrB,MAAM,CAACwM,QAAP,KAAoBrM,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD;AAAE,iBAAKoM,QAAL,GAAgB,EAAhB;;AAAoB,iBAAI,IAAIpL,KAAR,IAAiBpB,MAAM,CAACwM,QAAxB,EAAkC;AAAE,kBAAInL,IAAG,SAAP;;AAASA,cAAAA,IAAG,GAAGD,KAAN;AAAa,mBAAKoL,QAAL,CAAclL,IAAd,CAAmBD,IAAnB;AAAyB;AAAC;;AAC1G,cAAIrB,MAAM,CAACyM,QAAP,KAAoBtM,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD;AAAE,iBAAKqM,QAAL,GAAgB,EAAhB;;AAAoB,iBAAI,IAAIrL,KAAR,IAAiBpB,MAAM,CAACyM,QAAxB,EAAkC;AAAE,kBAAIpL,IAAG,SAAP;;AAASA,cAAAA,IAAG,GAAGD,KAAN;AAAa,mBAAKqL,QAAL,CAAcnL,IAAd,CAAmBD,IAAnB;AAAyB;AAAC;;AAC1G,cAAIrB,MAAM,CAAC0M,SAAP,KAAqBvM,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD;AAAE,iBAAKsM,SAAL,GAAiB,EAAjB;;AAAqB,iBAAI,IAAItL,KAAR,IAAiBpB,MAAM,CAAC0M,SAAxB,EAAmC;AAAE,kBAAIrL,IAAG,SAAP;;AAASA,cAAAA,IAAG,GAAGD,KAAN;AAAa,mBAAKsL,SAAL,CAAepL,IAAf,CAAoBD,IAApB;AAA0B;AAAC;;AAC7G,cAAIrB,MAAM,CAAC2M,YAAP,KAAwBxM,SAA5B,EAAuC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC5D;AAAE,iBAAKuM,YAAL,GAAoB,EAApB;;AAAwB,iBAAI,IAAIvL,KAAR,IAAiBpB,MAAM,CAAC2M,YAAxB,EAAsC;AAAE,kBAAItL,IAAG,SAAP;;AAASA,cAAAA,IAAG,GAAGD,KAAN;AAAa,mBAAKuL,YAAL,CAAkBrL,IAAlB,CAAuBD,IAAvB;AAA6B;AAAC;AACzH;;AAmCDhB,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAStB;;AA/DqB,O;;gCAsEbzD,c,GAAN,MAAMA,cAAN,CAAqB;AAExBkD,QAAAA,WAAW,CAACC,MAAD,EAAc;AASzB;AACJ;AACA;AAX6B,eAYhBiB,EAZgB;;AAazB;AACJ;AACA;AAf6B,eAgBhB4G,EAhBgB;;AAiBzB;AACJ;AACA;AAnB6B,eAoBhB9D,GApBgB;;AACrB,cAAI/D,MAAM,CAACiB,EAAP,KAAcd,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKa,EAAL,GAAUjB,MAAM,CAACiB,EAAjB;;AACA,cAAIjB,MAAM,CAAC6H,EAAP,KAAc1H,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKyH,EAAL,GAAU7H,MAAM,CAAC6H,EAAjB;;AACA,cAAI7H,MAAM,CAAC+D,GAAP,KAAe5D,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAK2D,GAAL,GAAW/D,MAAM,CAAC+D,GAAlB;AACH;;AAeD1D,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAItB;;AA5BuB,O;;0BAmCfxD,Q,GAAN,MAAMA,QAAN,CAAe;AAElBiD,QAAAA,WAAW,CAACC,MAAD,EAAc;AAuBzB;AACJ;AACA;AAzB6B,eA0BhBiB,EA1BgB;;AA2BzB;AACJ;AACA;AA7B6B,eA8BhB2L,SA9BgB;;AA+BzB;AACJ;AACA;AAjC6B,eAkChBlL,IAlCgB;;AAmCzB;AACJ;AACA;AArC6B,eAsChB2F,IAtCgB;;AAuCzB;AACJ;AACA;AAzC6B,eA0ChBwF,QA1CgB;;AA2CzB;AACJ;AACA;AA7C6B,eA8ChBzF,WA9CgB;;AA+CzB;AACJ;AACA;AAjD6B,eAkDhBsB,OAlDgB;;AAmDzB;AACJ;AACA;AArD6B,eAsDhBoE,UAtDgB;;AAuDzB;AACJ;AACA;AAzD6B,eA0DhBvG,OA1DgB;;AA2DzB;AACJ;AACA;AA7D6B,eA8DhBwG,SA9DgB;;AACrB,cAAI/M,MAAM,CAACiB,EAAP,KAAcd,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKa,EAAL,GAAUjB,MAAM,CAACiB,EAAjB;;AACA,cAAIjB,MAAM,CAACgN,UAAP,KAAsB7M,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKwM,SAAL,GAAiB5M,MAAM,CAACgN,UAAxB;;AACA,cAAIhN,MAAM,CAAC0B,IAAP,KAAgBvB,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKsB,IAAL,GAAY1B,MAAM,CAAC0B,IAAnB;;AACA,cAAI1B,MAAM,CAACqH,IAAP,KAAgBlH,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKiH,IAAL,GAAYrH,MAAM,CAACqH,IAAnB;;AACA,cAAIrH,MAAM,CAAC6M,QAAP,KAAoB1M,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAKyM,QAAL,GAAgB7M,MAAM,CAAC6M,QAAvB;;AACA,cAAI7M,MAAM,CAACoH,WAAP,KAAuBjH,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKgH,WAAL,GAAmBpH,MAAM,CAACoH,WAA1B;;AACA,cAAIpH,MAAM,CAAC0I,OAAP,KAAmBvI,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAKsI,OAAL,GAAe1I,MAAM,CAAC0I,OAAtB;;AACA,cAAI1I,MAAM,CAAC8M,UAAP,KAAsB3M,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D;AAAE,iBAAK0M,UAAL,GAAkB,EAAlB;;AAAsB,iBAAI,IAAI1L,KAAR,IAAiBpB,MAAM,CAAC8M,UAAxB,EAAoC;AAAE,kBAAIzL,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAG,IAAI1F,aAAJ,CAAkByF,KAAlB,CAAN;AAAgC,mBAAK0L,UAAL,CAAgBxL,IAAhB,CAAqBD,GAArB;AAA2B;AAAC;;AACnI,cAAIrB,MAAM,CAACuG,OAAP,KAAmBpG,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD;AAAE,iBAAKmG,OAAL,GAAe,EAAf;;AAAmB,iBAAI,IAAInF,MAAR,IAAiBpB,MAAM,CAACuG,OAAxB,EAAiC;AAAE,kBAAIlF,IAAG,SAAP;;AAASA,cAAAA,IAAG,GAAG,IAAI5F,WAAJ,CAAgB2F,MAAhB,CAAN;AAA8B,mBAAKmF,OAAL,CAAajF,IAAb,CAAkBD,IAAlB;AAAwB;AAAC;;AACxH,cAAIrB,MAAM,CAAC+M,SAAP,KAAqB5M,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD;AAAE,iBAAK2M,SAAL,GAAiB,EAAjB;;AAAqB,iBAAI,IAAI3L,MAAR,IAAiBpB,MAAM,CAAC+M,SAAxB,EAAmC;AAAE,kBAAI1L,IAAG,SAAP;;AAASA,cAAAA,IAAG,GAAG,IAAI3F,aAAJ,CAAkB0F,MAAlB,CAAN;AAAgC,mBAAK2L,SAAL,CAAezL,IAAf,CAAoBD,IAApB;AAA0B;AAAC;AACnI;;AA2CDhB,QAAAA,OAAO,CAACC,MAAD,EAAgB;AAQnB,eAAK,IAAI6G,EAAT,IAAe,KAAK2F,UAApB,EAAgC;AAAE3F,YAAAA,EAAE,QAAF,IAAAA,EAAE,CAAE9G,OAAJ,CAAYC,MAAZ;AAAsB;;AACxD,eAAK,IAAI6G,IAAT,IAAe,KAAKZ,OAApB,EAA6B;AAAEY,YAAAA,IAAE,QAAF,IAAAA,IAAE,CAAE9G,OAAJ,CAAYC,MAAZ;AAAsB;;AACrD,eAAK,IAAI6G,IAAT,IAAe,KAAK4F,SAApB,EAA+B;AAAE5F,YAAAA,IAAE,QAAF,IAAAA,IAAE,CAAE9G,OAAJ,CAAYC,MAAZ;AAAsB;AAC1D;;AA7EiB,O;;0BAoFTvD,Q,GAAN,MAAMA,QAAN,CAAe;AAElBgD,QAAAA,WAAW,CAACC,MAAD,EAAc;AAqBzB;AACJ;AACA;AAvB6B,eAwBhBiB,EAxBgB;;AAyBzB;AACJ;AACA;AA3B6B,eA4BhBS,IA5BgB;;AA6BzB;AACJ;AACA;AA/B6B,eAgChBE,IAhCgB;;AAiCzB;AACJ;AACA;AAnC6B,eAoChByF,IApCgB;;AAqCzB;AACJ;AACA;AAvC6B,eAwChB4F,EAxCgB;;AAyCzB;AACJ;AACA;AA3C6B,eA4ChBC,MA5CgB;;AA6CzB;AACJ;AACA;AA/C6B,eAgDhBC,OAhDgB;;AAiDzB;AACJ;AACA;AAnD6B,eAoDhB3G,WApDgB;AAAA,eAqDhB4G,UArDgB;;AACrB,cAAIpN,MAAM,CAACiB,EAAP,KAAcd,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKa,EAAL,GAAUjB,MAAM,CAACiB,EAAjB;;AACA,cAAIjB,MAAM,CAAC0B,IAAP,KAAgBvB,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKsB,IAAL,GAAY1B,MAAM,CAAC0B,IAAnB;;AACA,cAAI1B,MAAM,CAAC4B,IAAP,KAAgBzB,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKwB,IAAL,GAAY5B,MAAM,CAAC4B,IAAnB;;AACA,cAAI5B,MAAM,CAACqH,IAAP,KAAgBlH,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKiH,IAAL,GAAYrH,MAAM,CAACqH,IAAnB;;AACA,cAAIrH,MAAM,CAACiN,EAAP,KAAc9M,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAK6M,EAAL,GAAUjN,MAAM,CAACiN,EAAjB;;AACA,cAAIjN,MAAM,CAACkN,MAAP,KAAkB/M,SAAtB,EAAiC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD,eAAK8M,MAAL,GAAclN,MAAM,CAACkN,MAArB;;AACA,cAAIlN,MAAM,CAACmN,OAAP,KAAmBhN,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAK+M,OAAL,GAAenN,MAAM,CAACmN,OAAtB;;AACA,cAAInN,MAAM,CAACwG,WAAP,KAAuBrG,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKoG,WAAL,GAAmBxG,MAAM,CAACwG,WAA1B;;AACA,cAAIxG,MAAM,CAACoN,UAAP,KAAsBjN,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D;AAAE,iBAAKgN,UAAL,GAAkB,EAAlB;;AAAsB,iBAAI,IAAIhM,KAAR,IAAiBpB,MAAM,CAACoN,UAAxB,EAAoC;AAAE,kBAAI/L,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAG,IAAInG,SAAJ,CAAckG,KAAd,CAAN;AAA4B,mBAAKgM,UAAL,CAAgB9L,IAAhB,CAAqBD,GAArB;AAA2B;AAAC;AAClI;;AAoCDhB,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAUtB;;AAnEiB,O;;0BA0ETtD,Q,GAAN,MAAMA,QAAN,CAAe;AAElB+C,QAAAA,WAAW,CAACC,MAAD,EAAc;AAiBzB;AACJ;AACA;AAnB6B,eAoBhBiB,EApBgB;;AAqBzB;AACJ;AACA;AAvB6B,eAwBhBoM,SAxBgB;;AAyBzB;AACJ;AACA;AA3B6B,eA4BhBC,QA5BgB;;AA6BzB;AACJ;AACA;AA/B6B,eAgChBnM,IAhCgB;;AAiCzB;AACJ;AACA;AAnC6B,eAoChBoM,YApCgB;;AAqCzB;AACJ;AACA;AAvC6B,eAwChBC,KAxCgB;;AAyCzB;AACJ;AACA;AA3C6B,eA4ChBC,YA5CgB;;AACrB,cAAIzN,MAAM,CAACiB,EAAP,KAAcd,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKa,EAAL,GAAUjB,MAAM,CAACiB,EAAjB;;AACA,cAAIjB,MAAM,CAACqN,SAAP,KAAqBlN,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKiN,SAAL,GAAiBrN,MAAM,CAACqN,SAAxB;;AACA,cAAIrN,MAAM,CAACsN,QAAP,KAAoBnN,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAKkN,QAAL,GAAgBtN,MAAM,CAACsN,QAAvB;;AACA,cAAItN,MAAM,CAACmB,IAAP,KAAgBhB,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKe,IAAL,GAAYnB,MAAM,CAACmB,IAAnB;;AACA,cAAInB,MAAM,CAACuN,YAAP,KAAwBpN,SAA5B,EAAuC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC5D,eAAKmN,YAAL,GAAoBvN,MAAM,CAACuN,YAA3B;;AACA,cAAIvN,MAAM,CAACwN,KAAP,KAAiBrN,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAKoN,KAAL,GAAaxN,MAAM,CAACwN,KAApB;;AACA,cAAIxN,MAAM,CAACyN,YAAP,KAAwBtN,SAA5B,EAAuC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC5D,eAAKqN,YAAL,GAAoBzN,MAAM,CAACyN,YAA3B;AACH;;AA+BDpN,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAQtB;;AAxDiB,O;;yBA+DTrD,O,GAAN,MAAMA,OAAN,CAAc;AAEjB8C,QAAAA,WAAW,CAACC,MAAD,EAAc;AAqCzB;AACJ;AACA;AAvC6B,eAwChB0N,MAxCgB;;AAyCzB;AACJ;AACA;AA3C6B,eA4ChBC,OA5CgB;;AA6CzB;AACJ;AACA;AA/C6B,eAgDhBC,SAhDgB;;AAiDzB;AACJ;AACA;AAnD6B,eAoDhBC,MApDgB;;AAqDzB;AACJ;AACA;AAvD6B,eAwDhBC,UAxDgB;;AAyDzB;AACJ;AACA;AA3D6B,eA4DhBC,QA5DgB;;AA6DzB;AACJ;AACA;AA/D6B,eAgEhBC,SAhEgB;;AAiEzB;AACJ;AACA;AAnE6B,eAoEhBC,QApEgB;;AAqEzB;AACJ;AACA;AAvE6B,eAwEhBC,UAxEgB;;AAyEzB;AACJ;AACA;AA3E6B,eA4EhBC,UA5EgB;;AA6EzB;AACJ;AACA;AA/E6B,eAgFhBC,QAhFgB;;AAiFzB;AACJ;AACA;AAnF6B,eAoFhBC,OApFgB;;AAqFzB;AACJ;AACA;AAvF6B,eAwFhBC,UAxFgB;;AAyFzB;AACJ;AACA;AA3F6B,eA4FhBC,QA5FgB;;AA6FzB;AACJ;AACA;AA/F6B,eAgGhBC,QAhGgB;;AAiGzB;AACJ;AACA;AAnG6B,eAoGhBC,SApGgB;;AAqGzB;AACJ;AACA;AAvG6B,eAwGhBC,SAxGgB;;AACrB,cAAI1O,MAAM,CAAC2O,OAAP,KAAmBxO,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAKsN,MAAL,GAAc1N,MAAM,CAAC2O,OAArB;;AACA,cAAI3O,MAAM,CAAC4O,QAAP,KAAoBzO,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAKuN,OAAL,GAAe3N,MAAM,CAAC4O,QAAtB;;AACA,cAAI5O,MAAM,CAAC6O,UAAP,KAAsB1O,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKwN,SAAL,GAAiB5N,MAAM,CAAC6O,UAAxB;;AACA,cAAI7O,MAAM,CAAC8O,OAAP,KAAmB3O,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAKyN,MAAL,GAAc7N,MAAM,CAAC8O,OAArB;;AACA,cAAI9O,MAAM,CAAC+O,WAAP,KAAuB5O,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAK0N,UAAL,GAAkB9N,MAAM,CAAC+O,WAAzB;;AACA,cAAI/O,MAAM,CAACgP,SAAP,KAAqB7O,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAK2N,QAAL,GAAgB/N,MAAM,CAACgP,SAAvB;;AACA,cAAIhP,MAAM,CAACiP,UAAP,KAAsB9O,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAK4N,SAAL,GAAiBhO,MAAM,CAACiP,UAAxB;;AACA,cAAIjP,MAAM,CAACkP,SAAP,KAAqB/O,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAK6N,QAAL,GAAgBjO,MAAM,CAACkP,SAAvB;;AACA,cAAIlP,MAAM,CAACmP,WAAP,KAAuBhP,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D;AAAE,iBAAK8N,UAAL,GAAkB,EAAlB;;AAAsB,iBAAI,IAAI9M,KAAR,IAAiBpB,MAAM,CAACmP,WAAxB,EAAqC;AAAE,kBAAI9N,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAK8M,UAAL,CAAgB5M,IAAhB,CAAqBD,GAArB;AAA2B;AAAC;;AACjH,cAAIrB,MAAM,CAACmO,UAAP,KAAsBhO,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAK+N,UAAL,GAAkBnO,MAAM,CAACmO,UAAzB;;AACA,cAAInO,MAAM,CAACoP,SAAP,KAAqBjP,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKgO,QAAL,GAAgBpO,MAAM,CAACoP,SAAvB;;AACA,cAAIpP,MAAM,CAACqP,QAAP,KAAoBlP,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAKiO,OAAL,GAAerO,MAAM,CAACqP,QAAtB;;AACA,cAAIrP,MAAM,CAACsP,WAAP,KAAuBnP,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKkO,UAAL,GAAkBtO,MAAM,CAACsP,WAAzB;;AACA,cAAItP,MAAM,CAACuP,SAAP,KAAqBpP,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKmO,QAAL,GAAgBvO,MAAM,CAACuP,SAAvB;;AACA,cAAIvP,MAAM,CAACwP,SAAP,KAAqBrP,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKoO,QAAL,GAAgBxO,MAAM,CAACwP,SAAvB;;AACA,cAAIxP,MAAM,CAACyP,UAAP,KAAsBtP,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKqO,SAAL,GAAiBzO,MAAM,CAACyP,UAAxB;;AACA,cAAIzP,MAAM,CAAC0P,UAAP,KAAsBvP,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKsO,SAAL,GAAiB1O,MAAM,CAAC0P,UAAxB;AACH;;AAuEDrP,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAkBtB;;AA9HgB,O;;0BAqIRpD,Q,GAAN,MAAMA,QAAN,CAAe;AAElB6C,QAAAA,WAAW,CAACC,MAAD,EAAc;AASzB;AACJ;AACA;AAX6B,eAYhBiB,EAZgB;;AAazB;AACJ;AACA;AAf6B,eAgBhB0O,GAhBgB;;AAiBzB;AACJ;AACA;AAnB6B,eAoBhBpO,KApBgB;;AACrB,cAAIvB,MAAM,CAACiB,EAAP,KAAcd,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKa,EAAL,GAAUjB,MAAM,CAACiB,EAAjB;;AACA,cAAIjB,MAAM,CAAC2P,GAAP,KAAexP,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKuP,GAAL,GAAW3P,MAAM,CAAC2P,GAAlB;;AACA,cAAI3P,MAAM,CAACuB,KAAP,KAAiBpB,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAKmB,KAAL,GAAavB,MAAM,CAACuB,KAApB;AACH;;AAeDlB,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAItB;;AA5BiB,O;;yBAmCTnD,O,GAAN,MAAMA,OAAN,CAAc;AAEjB4C,QAAAA,WAAW,CAACC,MAAD,EAAc;AA6BzB;AACJ;AACA;AA/B6B,eAgChBiB,EAhCgB;;AAiCzB;AACJ;AACA;AAnC6B,eAoChBsM,YApCgB;;AAqCzB;AACJ;AACA;AAvC6B,eAwChBC,KAxCgB;;AAyCzB;AACJ;AACA;AA3C6B,eA4ChBoC,SA5CgB;;AA6CzB;AACJ;AACA;AA/C6B,eAgDhBC,OAhDgB;;AAiDzB;AACJ;AACA;AAnD6B,eAoDhBC,QApDgB;;AAqDzB;AACJ;AACA;AAvD6B,eAwDhBC,SAxDgB;;AAyDzB;AACJ;AACA;AA3D6B,eA4DhB7O,GA5DgB;;AA6DzB;AACJ;AACA;AA/D6B,eAgEhB8O,GAhEgB;;AAiEzB;AACJ;AACA;AAnE6B,eAoEhBC,KApEgB;;AAqEzB;AACJ;AACA;AAvE6B,eAwEhBC,WAxEgB;;AAyEzB;AACJ;AACA;AA3E6B,eA4EhBC,YA5EgB;;AA6EzB;AACJ;AACA;AA/E6B,eAgFhBC,eAhFgB;;AACrB,cAAIpQ,MAAM,CAACiB,EAAP,KAAcd,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKa,EAAL,GAAUjB,MAAM,CAACiB,EAAjB;;AACA,cAAIjB,MAAM,CAACuN,YAAP,KAAwBpN,SAA5B,EAAuC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC5D,eAAKmN,YAAL,GAAoBvN,MAAM,CAACuN,YAA3B;;AACA,cAAIvN,MAAM,CAACwN,KAAP,KAAiBrN,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAKoN,KAAL,GAAaxN,MAAM,CAACwN,KAApB;;AACA,cAAIxN,MAAM,CAAC4P,SAAP,KAAqBzP,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKwP,SAAL,GAAiB5P,MAAM,CAAC4P,SAAxB;;AACA,cAAI5P,MAAM,CAAC6P,OAAP,KAAmB1P,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAKyP,OAAL,GAAe7P,MAAM,CAAC6P,OAAtB;;AACA,cAAI7P,MAAM,CAAC8P,QAAP,KAAoB3P,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAK0P,QAAL,GAAgB9P,MAAM,CAAC8P,QAAvB;;AACA,cAAI9P,MAAM,CAAC+P,SAAP,KAAqB5P,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAK2P,SAAL,GAAiB/P,MAAM,CAAC+P,SAAxB;;AACA,cAAI/P,MAAM,CAACkB,GAAP,KAAef,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKc,GAAL,GAAWlB,MAAM,CAACkB,GAAlB;;AACA,cAAIlB,MAAM,CAACgQ,GAAP,KAAe7P,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAK4P,GAAL,GAAWhQ,MAAM,CAACgQ,GAAlB;;AACA,cAAIhQ,MAAM,CAACiQ,KAAP,KAAiB9P,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAK6P,KAAL,GAAajQ,MAAM,CAACiQ,KAApB;;AACA,cAAIjQ,MAAM,CAACkQ,WAAP,KAAuB/P,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAK8P,WAAL,GAAmBlQ,MAAM,CAACkQ,WAA1B;;AACA,cAAIlQ,MAAM,CAACmQ,YAAP,KAAwBhQ,SAA5B,EAAuC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC5D,eAAK+P,YAAL,GAAoBnQ,MAAM,CAACmQ,YAA3B;;AACA,cAAInQ,MAAM,CAACoQ,eAAP,KAA2BjQ,SAA/B,EAA0C;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC/D,eAAKgQ,eAAL,GAAuBpQ,MAAM,CAACoQ,eAA9B;AACH;;AAuDD/P,QAAAA,OAAO,CAACC,MAAD,EAAgB,CActB;;AAlGgB,O;;gCAyGRlD,c,GAAN,MAAMA,cAAN,CAAqB;AAExB2C,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAOhBmB,IAPgB;AAAA,eAQhBH,KARgB;;AACrB,cAAIhB,MAAM,CAACmB,IAAP,KAAgBhB,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKe,IAAL,GAAYnB,MAAM,CAACmB,IAAnB;;AACA,cAAInB,MAAM,CAACgB,KAAP,KAAiBb,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD;AAAE,iBAAKY,KAAL,GAAa,EAAb;;AAAiB,iBAAI,IAAII,KAAR,IAAiBpB,MAAM,CAACgB,KAAxB,EAA+B;AAAE,kBAAIK,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAKJ,KAAL,CAAWM,IAAX,CAAgBD,GAAhB;AAAsB;AAAC;AACpG;;AAKDhB,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAGtB;;AAfuB,O;;8BAuBfjD,Y,GAAN,MAAMA,YAAN,CAAmB;AAGtB0C,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eADjBqQ,KACiB;AACrB,cAAIrQ,MAAM,CAACsQ,MAAP,IAAiB,CAArB,EAAwB,MAAM,IAAIlQ,KAAJ,CAAU,+BAAV,CAAN;AACxB,eAAKiQ,KAAL,GAAa,IAAI7T,aAAJ,CAAkBwD,MAAM,CAAC,CAAD,CAAxB,CAAb;AACH;;AAEDuQ,QAAAA,OAAO,GAAkB;AAAE,iBAAO,KAAKF,KAAZ;AAAoB;AAE/C;AACJ;AACA;;;AACsB,YAAbxF,aAAa,GAAW;AAAE,iBAAO,KAAKwF,KAAL,CAAWxF,aAAlB;AAAkC;AACjE;AACJ;AACA;;;AACkB,YAATC,SAAS,GAAW;AAAE,iBAAO,KAAKuF,KAAL,CAAWvF,SAAlB;AAA8B;AACzD;AACJ;AACA;;;AAC8B,YAArBC,qBAAqB,GAAW;AAAE,iBAAO,KAAKsF,KAAL,CAAWtF,qBAAlB;AAA0C;AACjF;AACJ;AACA;;;AAC2B,YAAlBC,kBAAkB,GAAW;AAAE,iBAAO,KAAKqF,KAAL,CAAWrF,kBAAlB;AAAuC;AAC3E;AACJ;AACA;;;AACyB,YAAhBC,gBAAgB,GAAW;AAAE,iBAAO,KAAKoF,KAAL,CAAWpF,gBAAlB;AAAqC;AACvE;AACJ;AACA;;;AAC0B,YAAjBC,iBAAiB,GAAW;AAAE,iBAAO,KAAKmF,KAAL,CAAWnF,iBAAlB;AAAsC;;AAEzE7K,QAAAA,OAAO,CAACC,MAAD,EACP;AACI,eAAK+P,KAAL,CAAWhQ,OAAX,CAAmBC,MAAnB;AACH;;AAtCqB,O;;gCA6CbhD,c,GAAN,MAAMA,cAAN,CAAqB;AAGxByC,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBwQ,SAEiB;AACrB,eAAKA,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBzQ,MAAnB,EAA2B;AACvB,gBAAI0Q,EAAmB,SAAvB;;AACAA,YAAAA,EAAE,GAAG,IAAIpU,eAAJ,CAAoBmU,OAApB,CAAL;;AACA,iBAAKD,SAAL,CAAelP,IAAf,CAAoBoP,EAApB;AACH;AACJ;;AAEDC,QAAAA,WAAW,GAAsB;AAAE,iBAAO,KAAKH,SAAZ;AAAuB;;AAE1DI,QAAAA,GAAG,CAACC,KAAD,EAA6C;AAAE,iBAAO,KAAKL,SAAL,CAAeK,KAAf,CAAP;AAA8B;;AAEhFxQ,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKwQ,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAACzQ,OAAL,CAAaC,MAAb;AACH;AACJ;;AArBuB,O;;sBA4Bf/C,I,GAAN,MAAMA,IAAN,CAAW;AAGdwC,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBwQ,SAEiB;AACrB,eAAKA,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBzQ,MAAnB,EAA2B;AACvB,gBAAI0Q,EAAM,SAAV;;AACAA,YAAAA,EAAE,GAAG,IAAIlV,EAAJ,CAAOiV,OAAP,CAAL;;AACA,iBAAKD,SAAL,CAAelP,IAAf,CAAoBoP,EAApB;AACH;AACJ;;AAEDC,QAAAA,WAAW,GAAS;AAAE,iBAAO,KAAKH,SAAZ;AAAuB;;AAE7CI,QAAAA,GAAG,CAACC,KAAD,EAAgC;AAAE,iBAAO,KAAKL,SAAL,CAAeK,KAAf,CAAP;AAA8B;;AAEnExQ,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKwQ,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAACzQ,OAAL,CAAaC,MAAb;AACH;AACJ;;AArBa,O;;yBA4BL9C,O,GAAN,MAAMA,OAAN,CAAc;AAGjBuC,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBwQ,SAEiB;AACrB,eAAKA,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBzQ,MAAnB,EAA2B;AACvB,gBAAI0Q,EAAY,SAAhB;;AACAA,YAAAA,EAAE,GAAG,IAAI5T,QAAJ,CAAa2T,OAAb,CAAL;;AACA,iBAAKD,SAAL,CAAelP,IAAf,CAAoBoP,EAApB;AACH;AACJ;;AAEDC,QAAAA,WAAW,GAAe;AAAE,iBAAO,KAAKH,SAAZ;AAAuB;;AAEnDI,QAAAA,GAAG,CAACC,KAAD,EAAsC;AAAE,iBAAO,KAAKL,SAAL,CAAeK,KAAf,CAAP;AAA8B;;AAEzExQ,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKwQ,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAACzQ,OAAL,CAAaC,MAAb;AACH;AACJ;;AArBgB,O;;2BA4BR7C,S,GAAN,MAAMA,SAAN,CAAgB;AAGnBsC,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjB+Q,QAEiB;AAAA,eADjBP,SACiB;AACrB,eAAKO,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKR,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBzQ,MAAnB,EAA2B;AACvB,gBAAI0Q,EAAW,SAAf;;AACAA,YAAAA,EAAE,GAAG,IAAI3U,OAAJ,CAAY0U,OAAZ,CAAL;;AACA,iBAAKD,SAAL,CAAelP,IAAf,CAAoBoP,EAApB;;AACA,iBAAKK,QAAL,CAAcE,GAAd,CAAkBP,EAAE,CAACzP,EAArB,EAAyByP,EAAzB;AACH;AACJ;;AAEDQ,QAAAA,UAAU,GAAyB;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC5DJ,QAAAA,WAAW,GAAc;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEnDI,QAAAA,GAAG,CAACO,GAAD,EAAmC;AAAE,iBAAO,KAAKJ,QAAL,CAAcH,GAAd,CAAkBO,GAAlB,CAAP;AAAgC;;AAExE9Q,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKwQ,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAACzQ,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBkB,O;;6BA+BV5C,W,GAAN,MAAMA,WAAN,CAAkB;AAGrBqC,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjB+Q,QAEiB;AAAA,eADjBP,SACiB;AACrB,eAAKO,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKR,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBzQ,MAAnB,EAA2B;AACvB,gBAAI0Q,EAAa,SAAjB;;AACAA,YAAAA,EAAE,GAAG,IAAI1U,SAAJ,CAAcyU,OAAd,CAAL;;AACA,iBAAKD,SAAL,CAAelP,IAAf,CAAoBoP,EAApB;;AACA,iBAAKK,QAAL,CAAcE,GAAd,CAAkBP,EAAE,CAACzP,EAArB,EAAyByP,EAAzB;AACH;AACJ;;AAEDQ,QAAAA,UAAU,GAA2B;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC9DJ,QAAAA,WAAW,GAAgB;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAErDI,QAAAA,GAAG,CAACO,GAAD,EAAqC;AAAE,iBAAO,KAAKJ,QAAL,CAAcH,GAAd,CAAkBO,GAAlB,CAAP;AAAgC;;AAE1E9Q,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKwQ,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAACzQ,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBoB,O;;6BA+BZ3C,W,GAAN,MAAMA,WAAN,CAAkB;AAGrBoC,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjB+Q,QAEiB;AAAA,eADjBP,SACiB;AACrB,eAAKO,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKR,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBzQ,MAAnB,EAA2B;AACvB,gBAAI0Q,EAAa,SAAjB;;AACAA,YAAAA,EAAE,GAAG,IAAIzU,SAAJ,CAAcwU,OAAd,CAAL;;AACA,iBAAKD,SAAL,CAAelP,IAAf,CAAoBoP,EAApB;;AACA,iBAAKK,QAAL,CAAcE,GAAd,CAAkBP,EAAE,CAACzP,EAArB,EAAyByP,EAAzB;AACH;AACJ;;AAEDQ,QAAAA,UAAU,GAA2B;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC9DJ,QAAAA,WAAW,GAAgB;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAErDI,QAAAA,GAAG,CAACO,GAAD,EAAqC;AAAE,iBAAO,KAAKJ,QAAL,CAAcH,GAAd,CAAkBO,GAAlB,CAAP;AAAgC;;AAE1E9Q,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKwQ,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAACzQ,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBoB,O;;8BA+BZ1C,Y,GAAN,MAAMA,YAAN,CAAmB;AAGtBmC,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjB+Q,QAEiB;AAAA,eADjBP,SACiB;AACrB,eAAKO,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKR,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBzQ,MAAnB,EAA2B;AACvB,gBAAI0Q,EAAc,SAAlB;;AACAA,YAAAA,EAAE,GAAG,IAAIxU,UAAJ,CAAeuU,OAAf,CAAL;;AACA,iBAAKD,SAAL,CAAelP,IAAf,CAAoBoP,EAApB;;AACA,iBAAKK,QAAL,CAAcE,GAAd,CAAkBP,EAAE,CAACzP,EAArB,EAAyByP,EAAzB;AACH;AACJ;;AAEDQ,QAAAA,UAAU,GAA4B;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC/DJ,QAAAA,WAAW,GAAiB;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEtDI,QAAAA,GAAG,CAACO,GAAD,EAAsC;AAAE,iBAAO,KAAKJ,QAAL,CAAcH,GAAd,CAAkBO,GAAlB,CAAP;AAAgC;;AAE3E9Q,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKwQ,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAACzQ,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBqB,O;;6BA+BbzC,W,GAAN,MAAMA,WAAN,CAAkB;AAGrBkC,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjB+Q,QAEiB;AAAA,eADjBP,SACiB;AACrB,eAAKO,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKR,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBzQ,MAAnB,EAA2B;AACvB,gBAAI0Q,EAAa,SAAjB;;AACAA,YAAAA,EAAE,GAAG,IAAIvU,SAAJ,CAAcsU,OAAd,CAAL;;AACA,iBAAKD,SAAL,CAAelP,IAAf,CAAoBoP,EAApB;;AACA,iBAAKK,QAAL,CAAcE,GAAd,CAAkBP,EAAE,CAACzP,EAArB,EAAyByP,EAAzB;AACH;AACJ;;AAEDQ,QAAAA,UAAU,GAA2B;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC9DJ,QAAAA,WAAW,GAAgB;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAErDI,QAAAA,GAAG,CAACO,GAAD,EAAqC;AAAE,iBAAO,KAAKJ,QAAL,CAAcH,GAAd,CAAkBO,GAAlB,CAAP;AAAgC;;AAE1E9Q,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKwQ,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAACzQ,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBoB,O;;4BA+BZxC,U,GAAN,MAAMA,UAAN,CAAiB;AAGpBiC,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjB+Q,QAEiB;AAAA,eADjBP,SACiB;AACrB,eAAKO,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKR,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBzQ,MAAnB,EAA2B;AACvB,gBAAI0Q,EAAY,SAAhB;;AACAA,YAAAA,EAAE,GAAG,IAAItU,QAAJ,CAAaqU,OAAb,CAAL;;AACA,iBAAKD,SAAL,CAAelP,IAAf,CAAoBoP,EAApB;;AACA,iBAAKK,QAAL,CAAcE,GAAd,CAAkBP,EAAE,CAACzP,EAArB,EAAyByP,EAAzB;AACH;AACJ;;AAEDQ,QAAAA,UAAU,GAA0B;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC7DJ,QAAAA,WAAW,GAAe;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEpDI,QAAAA,GAAG,CAACO,GAAD,EAAoC;AAAE,iBAAO,KAAKJ,QAAL,CAAcH,GAAd,CAAkBO,GAAlB,CAAP;AAAgC;;AAEzE9Q,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKwQ,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAACzQ,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBmB,O;;4BA+BXvC,U,GAAN,MAAMA,UAAN,CAAiB;AAGpBgC,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjB+Q,QAEiB;AAAA,eADjBP,SACiB;AACrB,eAAKO,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKR,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBzQ,MAAnB,EAA2B;AACvB,gBAAI0Q,EAAY,SAAhB;;AACAA,YAAAA,EAAE,GAAG,IAAIrU,QAAJ,CAAaoU,OAAb,CAAL;;AACA,iBAAKD,SAAL,CAAelP,IAAf,CAAoBoP,EAApB;;AACA,iBAAKK,QAAL,CAAcE,GAAd,CAAkBP,EAAE,CAACzP,EAArB,EAAyByP,EAAzB;AACH;AACJ;;AAEDQ,QAAAA,UAAU,GAA0B;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC7DJ,QAAAA,WAAW,GAAe;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEpDI,QAAAA,GAAG,CAACO,GAAD,EAAoC;AAAE,iBAAO,KAAKJ,QAAL,CAAcH,GAAd,CAAkBO,GAAlB,CAAP;AAAgC;;AAEzE9Q,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKwQ,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAACzQ,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBmB,O;;+BA+BXtC,a,GAAN,MAAMA,aAAN,CAAoB;AAGvB+B,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjB+Q,QAEiB;AAAA,eADjBP,SACiB;AACrB,eAAKO,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKR,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBzQ,MAAnB,EAA2B;AACvB,gBAAI0Q,EAAe,SAAnB;;AACAA,YAAAA,EAAE,GAAG,IAAInU,WAAJ,CAAgBkU,OAAhB,CAAL;;AACA,iBAAKD,SAAL,CAAelP,IAAf,CAAoBoP,EAApB;;AACA,iBAAKK,QAAL,CAAcE,GAAd,CAAkBP,EAAE,CAAClO,EAArB,EAAyBkO,EAAzB;AACH;AACJ;;AAEDQ,QAAAA,UAAU,GAA6B;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAChEJ,QAAAA,WAAW,GAAkB;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEvDI,QAAAA,GAAG,CAACO,GAAD,EAAuC;AAAE,iBAAO,KAAKJ,QAAL,CAAcH,GAAd,CAAkBO,GAAlB,CAAP;AAAgC;;AAE5E9Q,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKwQ,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAACzQ,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBsB,O;;2BA+BdrC,S,GAAN,MAAMA,SAAN,CAAgB;AAGnB8B,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjB+Q,QAEiB;AAAA,eADjBP,SACiB;AACrB,eAAKO,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKR,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBzQ,MAAnB,EAA2B;AACvB,gBAAI0Q,EAAW,SAAf;;AACAA,YAAAA,EAAE,GAAG,IAAIjU,OAAJ,CAAYgU,OAAZ,CAAL;;AACA,iBAAKD,SAAL,CAAelP,IAAf,CAAoBoP,EAApB;;AACA,iBAAKK,QAAL,CAAcE,GAAd,CAAkBP,EAAE,CAACzP,EAArB,EAAyByP,EAAzB;AACH;AACJ;;AAEDQ,QAAAA,UAAU,GAAyB;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC5DJ,QAAAA,WAAW,GAAc;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEnDI,QAAAA,GAAG,CAACO,GAAD,EAAmC;AAAE,iBAAO,KAAKJ,QAAL,CAAcH,GAAd,CAAkBO,GAAlB,CAAP;AAAgC;;AAExE9Q,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKwQ,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAACzQ,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBkB,O;;4BA+BVpC,U,GAAN,MAAMA,UAAN,CAAiB;AAGpB6B,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjB+Q,QAEiB;AAAA,eADjBP,SACiB;AACrB,eAAKO,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKR,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBzQ,MAAnB,EAA2B;AACvB,gBAAI0Q,EAAY,SAAhB;;AACAA,YAAAA,EAAE,GAAG,IAAIhU,QAAJ,CAAa+T,OAAb,CAAL;;AACA,iBAAKD,SAAL,CAAelP,IAAf,CAAoBoP,EAApB;;AACA,iBAAKK,QAAL,CAAcE,GAAd,CAAkBP,EAAE,CAACzP,EAArB,EAAyByP,EAAzB;AACH;AACJ;;AAEDQ,QAAAA,UAAU,GAA0B;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC7DJ,QAAAA,WAAW,GAAe;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEpDI,QAAAA,GAAG,CAACO,GAAD,EAAoC;AAAE,iBAAO,KAAKJ,QAAL,CAAcH,GAAd,CAAkBO,GAAlB,CAAP;AAAgC;;AAEzE9Q,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKwQ,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAACzQ,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBmB,O;;iCA+BXnC,e,GAAN,MAAMA,eAAN,CAAsB;AAGzB4B,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjB+Q,QAEiB;AAAA,eADjBP,SACiB;AACrB,eAAKO,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKR,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBzQ,MAAnB,EAA2B;AACvB,gBAAI0Q,EAAiB,SAArB;;AACAA,YAAAA,EAAE,GAAG,IAAI/T,aAAJ,CAAkB8T,OAAlB,CAAL;;AACA,iBAAKD,SAAL,CAAelP,IAAf,CAAoBoP,EAApB;;AACA,iBAAKK,QAAL,CAAcE,GAAd,CAAkBP,EAAE,CAACzP,EAArB,EAAyByP,EAAzB;AACH;AACJ;;AAEDQ,QAAAA,UAAU,GAA+B;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAClEJ,QAAAA,WAAW,GAAoB;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEzDI,QAAAA,GAAG,CAACO,GAAD,EAAyC;AAAE,iBAAO,KAAKJ,QAAL,CAAcH,GAAd,CAAkBO,GAAlB,CAAP;AAAgC;;AAE9E9Q,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKwQ,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAACzQ,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBwB,O;;gCA+BhBlC,c,GAAN,MAAMA,cAAN,CAAqB;AAGxB2B,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjB+Q,QAEiB;AAAA,eADjBP,SACiB;AACrB,eAAKO,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKR,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBzQ,MAAnB,EAA2B;AACvB,gBAAI0Q,EAAgB,SAApB;;AACAA,YAAAA,EAAE,GAAG,IAAI9T,YAAJ,CAAiB6T,OAAjB,CAAL;;AACA,iBAAKD,SAAL,CAAelP,IAAf,CAAoBoP,EAApB;;AACA,iBAAKK,QAAL,CAAcE,GAAd,CAAkBP,EAAE,CAACzP,EAArB,EAAyByP,EAAzB;AACH;AACJ;;AAEDQ,QAAAA,UAAU,GAA8B;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AACjEJ,QAAAA,WAAW,GAAmB;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAExDI,QAAAA,GAAG,CAACO,GAAD,EAAwC;AAAE,iBAAO,KAAKJ,QAAL,CAAcH,GAAd,CAAkBO,GAAlB,CAAP;AAAgC;;AAE7E9Q,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKwQ,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAACzQ,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBuB,O;;kCA+BfjC,gB,GAAN,MAAMA,gBAAN,CAAuB;AAG1B0B,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjB+Q,QAEiB;AAAA,eADjBP,SACiB;AACrB,eAAKO,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKR,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBzQ,MAAnB,EAA2B;AACvB,gBAAI0Q,EAAkB,SAAtB;;AACAA,YAAAA,EAAE,GAAG,IAAI7T,cAAJ,CAAmB4T,OAAnB,CAAL;;AACA,iBAAKD,SAAL,CAAelP,IAAf,CAAoBoP,EAApB;;AACA,iBAAKK,QAAL,CAAcE,GAAd,CAAkBP,EAAE,CAACzP,EAArB,EAAyByP,EAAzB;AACH;AACJ;;AAEDQ,QAAAA,UAAU,GAAgC;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AACnEJ,QAAAA,WAAW,GAAqB;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAE1DI,QAAAA,GAAG,CAACO,GAAD,EAA0C;AAAE,iBAAO,KAAKJ,QAAL,CAAcH,GAAd,CAAkBO,GAAlB,CAAP;AAAgC;;AAE/E9Q,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKwQ,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAACzQ,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxByB,O;;4BA+BjBhC,U,GAAN,MAAMA,UAAN,CAAiB;AAGpByB,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjB+Q,QAEiB;AAAA,eADjBP,SACiB;AACrB,eAAKO,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKR,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBzQ,MAAnB,EAA2B;AACvB,gBAAI0Q,EAAY,SAAhB;;AACAA,YAAAA,EAAE,GAAG,IAAI3T,QAAJ,CAAa0T,OAAb,CAAL;;AACA,iBAAKD,SAAL,CAAelP,IAAf,CAAoBoP,EAApB;;AACA,iBAAKK,QAAL,CAAcE,GAAd,CAAkBP,EAAE,CAACzP,EAArB,EAAyByP,EAAzB;AACH;AACJ;;AAEDQ,QAAAA,UAAU,GAA0B;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC7DJ,QAAAA,WAAW,GAAe;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEpDI,QAAAA,GAAG,CAACO,GAAD,EAAoC;AAAE,iBAAO,KAAKJ,QAAL,CAAcH,GAAd,CAAkBO,GAAlB,CAAP;AAAgC;;AAEzE9Q,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKwQ,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAACzQ,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBmB,O;;4BA+BX/B,U,GAAN,MAAMA,UAAN,CAAiB;AAGpBwB,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjB+Q,QAEiB;AAAA,eADjBP,SACiB;AACrB,eAAKO,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKR,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBzQ,MAAnB,EAA2B;AACvB,gBAAI0Q,EAAY,SAAhB;;AACAA,YAAAA,EAAE,GAAG,IAAI1T,QAAJ,CAAayT,OAAb,CAAL;;AACA,iBAAKD,SAAL,CAAelP,IAAf,CAAoBoP,EAApB;;AACA,iBAAKK,QAAL,CAAcE,GAAd,CAAkBP,EAAE,CAACzP,EAArB,EAAyByP,EAAzB;AACH;AACJ;;AAEDQ,QAAAA,UAAU,GAA0B;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC7DJ,QAAAA,WAAW,GAAe;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEpDI,QAAAA,GAAG,CAACO,GAAD,EAAoC;AAAE,iBAAO,KAAKJ,QAAL,CAAcH,GAAd,CAAkBO,GAAlB,CAAP;AAAgC;;AAEzE9Q,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKwQ,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAACzQ,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBmB,O;;2BA+BX9B,S,GAAN,MAAMA,SAAN,CAAgB;AAGnBuB,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjB+Q,QAEiB;AAAA,eADjBP,SACiB;AACrB,eAAKO,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKR,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBzQ,MAAnB,EAA2B;AACvB,gBAAI0Q,EAAW,SAAf;;AACAA,YAAAA,EAAE,GAAG,IAAIzT,OAAJ,CAAYwT,OAAZ,CAAL;;AACA,iBAAKD,SAAL,CAAelP,IAAf,CAAoBoP,EAApB;;AACA,iBAAKK,QAAL,CAAcE,GAAd,CAAkBP,EAAE,CAAChD,MAArB,EAA6BgD,EAA7B;AACH;AACJ;;AAEDQ,QAAAA,UAAU,GAAyB;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC5DJ,QAAAA,WAAW,GAAc;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEnDI,QAAAA,GAAG,CAACO,GAAD,EAAmC;AAAE,iBAAO,KAAKJ,QAAL,CAAcH,GAAd,CAAkBO,GAAlB,CAAP;AAAgC;;AAExE9Q,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKwQ,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAACzQ,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBkB,O;;4BA+BV7B,U,GAAN,MAAMA,UAAN,CAAiB;AAGpBsB,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjB+Q,QAEiB;AAAA,eADjBP,SACiB;AACrB,eAAKO,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKR,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBzQ,MAAnB,EAA2B;AACvB,gBAAI0Q,EAAY,SAAhB;;AACAA,YAAAA,EAAE,GAAG,IAAIxT,QAAJ,CAAauT,OAAb,CAAL;;AACA,iBAAKD,SAAL,CAAelP,IAAf,CAAoBoP,EAApB;;AACA,iBAAKK,QAAL,CAAcE,GAAd,CAAkBP,EAAE,CAACzP,EAArB,EAAyByP,EAAzB;AACH;AACJ;;AAEDQ,QAAAA,UAAU,GAA0B;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC7DJ,QAAAA,WAAW,GAAe;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEpDI,QAAAA,GAAG,CAACO,GAAD,EAAoC;AAAE,iBAAO,KAAKJ,QAAL,CAAcH,GAAd,CAAkBO,GAAlB,CAAP;AAAgC;;AAEzE9Q,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKwQ,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAACzQ,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBmB,O;;2BA+BX5B,S,GAAN,MAAMA,SAAN,CAAgB;AAGnBqB,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjB+Q,QAEiB;AAAA,eADjBP,SACiB;AACrB,eAAKO,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKR,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBzQ,MAAnB,EAA2B;AACvB,gBAAI0Q,EAAW,SAAf;;AACAA,YAAAA,EAAE,GAAG,IAAIvT,OAAJ,CAAYsT,OAAZ,CAAL;;AACA,iBAAKD,SAAL,CAAelP,IAAf,CAAoBoP,EAApB;;AACA,iBAAKK,QAAL,CAAcE,GAAd,CAAkBP,EAAE,CAACzP,EAArB,EAAyByP,EAAzB;AACH;AACJ;;AAEDQ,QAAAA,UAAU,GAAyB;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC5DJ,QAAAA,WAAW,GAAc;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEnDI,QAAAA,GAAG,CAACO,GAAD,EAAmC;AAAE,iBAAO,KAAKJ,QAAL,CAAcH,GAAd,CAAkBO,GAAlB,CAAP;AAAgC;;AAExE9Q,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKwQ,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAACzQ,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBkB,O;;wBAiCV3B,M,GAAN,MAAMA,MAAN,CAAa;AAEA,YAAZtB,YAAY,GAAkB;AAAE,iBAAO,KAAK+T,aAAZ;AAA2B;;AAE7C,YAAd9T,cAAc,GAAoB;AAAE,iBAAO,KAAK+T,eAAZ;AAA6B;;AAE7D,YAAJ9T,IAAI,GAAU;AAAE,iBAAO,KAAK+T,KAAZ;AAAmB;;AAE5B,YAAP9T,OAAO,GAAa;AAAE,iBAAO,KAAK+T,QAAZ;AAAsB;;AAEnC,YAAT9T,SAAS,GAAe;AAAE,iBAAO,KAAK+T,UAAZ;AAAwB;;AAEvC,YAAX9T,WAAW,GAAiB;AAAE,iBAAO,KAAK+T,YAAZ;AAA0B;;AAE7C,YAAX9T,WAAW,GAAiB;AAAE,iBAAO,KAAK+T,YAAZ;AAA0B;;AAE5C,YAAZ9T,YAAY,GAAkB;AAAE,iBAAO,KAAK+T,aAAZ;AAA2B;;AAEhD,YAAX9T,WAAW,GAAiB;AAAE,iBAAO,KAAK+T,YAAZ;AAA0B;;AAE9C,YAAV9T,UAAU,GAAgB;AAAE,iBAAO,KAAK+T,WAAZ;AAAyB;;AAE3C,YAAV9T,UAAU,GAAgB;AAAE,iBAAO,KAAK+T,WAAZ;AAAyB;;AAExC,YAAb9T,aAAa,GAAmB;AAAE,iBAAO,KAAK+T,cAAZ;AAA4B;;AAErD,YAAT9T,SAAS,GAAe;AAAE,iBAAO,KAAK+T,UAAZ;AAAwB;;AAExC,YAAV9T,UAAU,GAAgB;AAAE,iBAAO,KAAK+T,WAAZ;AAAyB;;AAEtC,YAAf9T,eAAe,GAAqB;AAAE,iBAAO,KAAK+T,gBAAZ;AAA8B;;AAEtD,YAAd9T,cAAc,GAAoB;AAAE,iBAAO,KAAK+T,eAAZ;AAA6B;;AAEjD,YAAhB9T,gBAAgB,GAAsB;AAAE,iBAAO,KAAK+T,iBAAZ;AAA+B;;AAE7D,YAAV9T,UAAU,GAAgB;AAAE,iBAAO,KAAK+T,WAAZ;AAAyB;;AAE3C,YAAV9T,UAAU,GAAgB;AAAE,iBAAO,KAAK+T,WAAZ;AAAyB;;AAE5C,YAAT9T,SAAS,GAAe;AAAE,iBAAO,KAAK+T,UAAZ;AAAwB;;AAExC,YAAV9T,UAAU,GAAgB;AAAE,iBAAO,KAAK+T,WAAZ;AAAyB;;AAE5C,YAAT9T,SAAS,GAAe;AAAE,iBAAO,KAAK+T,UAAZ;AAAwB;;AAEtD1S,QAAAA,WAAW,CAAC2S,MAAD,EAAqB;AAAA,eA7CxBtB,aA6CwB;AAAA,eA3CxBC,eA2CwB;AAAA,eAzCxBC,KAyCwB;AAAA,eAvCxBC,QAuCwB;AAAA,eArCxBC,UAqCwB;AAAA,eAnCxBC,YAmCwB;AAAA,eAjCxBC,YAiCwB;AAAA,eA/BxBC,aA+BwB;AAAA,eA7BxBC,YA6BwB;AAAA,eA3BxBC,WA2BwB;AAAA,eAzBxBC,WAyBwB;AAAA,eAvBxBC,cAuBwB;AAAA,eArBxBC,UAqBwB;AAAA,eAnBxBC,WAmBwB;AAAA,eAjBxBC,gBAiBwB;AAAA,eAfxBC,eAewB;AAAA,eAbxBC,iBAawB;AAAA,eAXxBC,WAWwB;AAAA,eATxBC,WASwB;AAAA,eAPxBC,UAOwB;AAAA,eALxBC,WAKwB;AAAA,eAHxBC,UAGwB;AAC5B,eAAKrB,aAAL,GAAqB,IAAI/T,YAAJ,CAAiBqV,MAAM,CAAC,cAAD,CAAvB,CAArB;AACA,eAAKrB,eAAL,GAAuB,IAAI/T,cAAJ,CAAmBoV,MAAM,CAAC,gBAAD,CAAzB,CAAvB;AACA,eAAKpB,KAAL,GAAa,IAAI/T,IAAJ,CAASmV,MAAM,CAAC,MAAD,CAAf,CAAb;AACA,eAAKnB,QAAL,GAAgB,IAAI/T,OAAJ,CAAYkV,MAAM,CAAC,SAAD,CAAlB,CAAhB;AACA,eAAKlB,UAAL,GAAkB,IAAI/T,SAAJ,CAAciV,MAAM,CAAC,WAAD,CAApB,CAAlB;AACA,eAAKjB,YAAL,GAAoB,IAAI/T,WAAJ,CAAgBgV,MAAM,CAAC,aAAD,CAAtB,CAApB;AACA,eAAKhB,YAAL,GAAoB,IAAI/T,WAAJ,CAAgB+U,MAAM,CAAC,aAAD,CAAtB,CAApB;AACA,eAAKf,aAAL,GAAqB,IAAI/T,YAAJ,CAAiB8U,MAAM,CAAC,cAAD,CAAvB,CAArB;AACA,eAAKd,YAAL,GAAoB,IAAI/T,WAAJ,CAAgB6U,MAAM,CAAC,aAAD,CAAtB,CAApB;AACA,eAAKb,WAAL,GAAmB,IAAI/T,UAAJ,CAAe4U,MAAM,CAAC,YAAD,CAArB,CAAnB;AACA,eAAKZ,WAAL,GAAmB,IAAI/T,UAAJ,CAAe2U,MAAM,CAAC,YAAD,CAArB,CAAnB;AACA,eAAKX,cAAL,GAAsB,IAAI/T,aAAJ,CAAkB0U,MAAM,CAAC,eAAD,CAAxB,CAAtB;AACA,eAAKV,UAAL,GAAkB,IAAI/T,SAAJ,CAAcyU,MAAM,CAAC,WAAD,CAApB,CAAlB;AACA,eAAKT,WAAL,GAAmB,IAAI/T,UAAJ,CAAewU,MAAM,CAAC,YAAD,CAArB,CAAnB;AACA,eAAKR,gBAAL,GAAwB,IAAI/T,eAAJ,CAAoBuU,MAAM,CAAC,iBAAD,CAA1B,CAAxB;AACA,eAAKP,eAAL,GAAuB,IAAI/T,cAAJ,CAAmBsU,MAAM,CAAC,gBAAD,CAAzB,CAAvB;AACA,eAAKN,iBAAL,GAAyB,IAAI/T,gBAAJ,CAAqBqU,MAAM,CAAC,kBAAD,CAA3B,CAAzB;AACA,eAAKL,WAAL,GAAmB,IAAI/T,UAAJ,CAAeoU,MAAM,CAAC,YAAD,CAArB,CAAnB;AACA,eAAKJ,WAAL,GAAmB,IAAI/T,UAAJ,CAAemU,MAAM,CAAC,YAAD,CAArB,CAAnB;AACA,eAAKH,UAAL,GAAkB,IAAI/T,SAAJ,CAAckU,MAAM,CAAC,WAAD,CAApB,CAAlB;AACA,eAAKF,WAAL,GAAmB,IAAI/T,UAAJ,CAAeiU,MAAM,CAAC,YAAD,CAArB,CAAnB;AACA,eAAKD,UAAL,GAAkB,IAAI/T,SAAJ,CAAcgU,MAAM,CAAC,WAAD,CAApB,CAAlB;;AAEA,eAAKtB,aAAL,CAAmB/Q,OAAnB,CAA2B,IAA3B;;AACA,eAAKgR,eAAL,CAAqBhR,OAArB,CAA6B,IAA7B;;AACA,eAAKiR,KAAL,CAAWjR,OAAX,CAAmB,IAAnB;;AACA,eAAKkR,QAAL,CAAclR,OAAd,CAAsB,IAAtB;;AACA,eAAKmR,UAAL,CAAgBnR,OAAhB,CAAwB,IAAxB;;AACA,eAAKoR,YAAL,CAAkBpR,OAAlB,CAA0B,IAA1B;;AACA,eAAKqR,YAAL,CAAkBrR,OAAlB,CAA0B,IAA1B;;AACA,eAAKsR,aAAL,CAAmBtR,OAAnB,CAA2B,IAA3B;;AACA,eAAKuR,YAAL,CAAkBvR,OAAlB,CAA0B,IAA1B;;AACA,eAAKwR,WAAL,CAAiBxR,OAAjB,CAAyB,IAAzB;;AACA,eAAKyR,WAAL,CAAiBzR,OAAjB,CAAyB,IAAzB;;AACA,eAAK0R,cAAL,CAAoB1R,OAApB,CAA4B,IAA5B;;AACA,eAAK2R,UAAL,CAAgB3R,OAAhB,CAAwB,IAAxB;;AACA,eAAK4R,WAAL,CAAiB5R,OAAjB,CAAyB,IAAzB;;AACA,eAAK6R,gBAAL,CAAsB7R,OAAtB,CAA8B,IAA9B;;AACA,eAAK8R,eAAL,CAAqB9R,OAArB,CAA6B,IAA7B;;AACA,eAAK+R,iBAAL,CAAuB/R,OAAvB,CAA+B,IAA/B;;AACA,eAAKgS,WAAL,CAAiBhS,OAAjB,CAAyB,IAAzB;;AACA,eAAKiS,WAAL,CAAiBjS,OAAjB,CAAyB,IAAzB;;AACA,eAAKkS,UAAL,CAAgBlS,OAAhB,CAAwB,IAAxB;;AACA,eAAKmS,WAAL,CAAiBnS,OAAjB,CAAyB,IAAzB;;AACA,eAAKoS,UAAL,CAAgBpS,OAAhB,CAAwB,IAAxB;AACH;;AA5Fe,O", "sourcesContent": ["\n//------------------------------------------------------------------------------\n// <auto-generated>\n//     This code was generated by a tool.\n//     Changes to this file may cause incorrect behavior and will be lost if\n//     the code is regenerated.\n// </auto-generated>\n//------------------------------------------------------------------------------\n\n\n \n/**\n * buff类型\n */\nexport enum BuffType {\n    /**\n     * 正面\n     */\n    Positive = 0,\n    /**\n     * 中性\n     */\n    Neutral = 1,\n    /**\n     * 负面\n     */\n    Negative = 2,\n}\n\n \n \n/**\n * 子弹来源\n */\nexport enum BulletSourceType {\n    /**\n     * 自机\n     */\n    MAINPLANE = 0,\n    /**\n     * 僚机\n     */\n    WINGPLANE = 1,\n    /**\n     * 敌机\n     */\n    ENEMYPLANE = 2,\n}\n\n \n \n/**\n * 子弹类型\n */\nexport enum BulletType {\n    /**\n     * 常规\n     */\n    NORMAL = 0,\n    /**\n     * 核弹\n     */\n    NUCLEAR = 1,\n}\n\n \n \n/**\n * 效果类型\n */\nexport enum EffectType {\n    /**\n     * 最大生命值%\n     */\n    AttrMaxHPPer = 1,\n    /**\n     * 最大生命值+\n     */\n    AttrMaxHPAdd = 2,\n    /**\n     * 生命恢复速度%\n     */\n    AttrHPRecoveryPer = 3,\n    /**\n     * 生命恢复速度+\n     */\n    AttrHPRecoveryAdd = 4,\n    /**\n     * 基于最大生命值的恢复速度%\n     */\n    AttrHPRecoveryMaxHPPerAdd = 5,\n    /**\n     * 回复最大生命值%\n     */\n    RecoveryMaxHPPer = 6,\n    /**\n     * 回复已损失生命%\n     */\n    RecoveryLoseHPPer = 7,\n    /**\n     * 回复指定生命值+\n     */\n    RecoveryHP = 8,\n    /**\n     * 攻击力%\n     */\n    AttrAttackPer = 9,\n    /**\n     * 攻击力+\n     */\n    AttrAttackAdd = 10,\n    /**\n     * 对Boss伤害%\n     */\n    AttrAttackBossPer = 11,\n    /**\n     * 对普通怪物伤害%\n     */\n    AttrAttackNormalPer = 12,\n    /**\n     * 幸运值%\n     */\n    AttrFortunatePer = 13,\n    /**\n     * 幸运值+\n     */\n    AttrFortunateAdd = 14,\n    /**\n     * 闪避率+\n     */\n    AttrMissAdd = 15,\n    /**\n     * 子弹伤害抗性%\n     */\n    AttrBulletHurtResistancePer = 16,\n    /**\n     * 子弹伤害抗性+\n     */\n    AttrBulletHurtResistanceAdd = 17,\n    /**\n     * 撞击伤害减免%\n     */\n    AttrCollisionHurtDerateAdd = 18,\n    /**\n     * 撞击伤害抗性%\n     */\n    AttrCollisionHurtResistancePer = 19,\n    /**\n     * 撞击伤害抗性+\n     */\n    AttrCollisionHurtResistanceAdd = 20,\n    /**\n     * 子弹伤害减免%\n     */\n    AttrBulletHurtDerateAdd = 21,\n    /**\n     * 结算得分%\n     */\n    AttrFinalScoreAdd = 22,\n    /**\n     * 击杀得分%\n     */\n    AttrKillScoreAdd = 23,\n    /**\n     * 能量回复%\n     */\n    AttrEnergyRecoveryPerAdd = 24,\n    /**\n     * 能量回复+\n     */\n    AttrEnergyRecoveryAdd = 25,\n    /**\n     * 拾取范围%\n     */\n    AttrPickRadiusPer = 26,\n    /**\n     * 拾取范围+\n     */\n    AttrPickRadius = 27,\n    /**\n     * 添加Buff\n     */\n    ApplyBuff = 28,\n    /**\n     * 免疫子弹伤害\n     */\n    ImmuneBulletHurt = 29,\n    /**\n     * 免疫撞击伤害\n     */\n    ImmuneCollisionHurt = 30,\n    /**\n     * 无视子弹\n     */\n    IgnoreBullet = 31,\n    /**\n     * 无视撞击\n     */\n    IgnoreCollision = 32,\n    /**\n     * 免疫核弹伤害\n     */\n    ImmuneBombHurt = 33,\n    /**\n     * 免疫主动技能\n     */\n    ImmuneActiveSkillHurt = 34,\n    /**\n     * 无敌\n     */\n    Invincible = 35,\n    /**\n     * 开局核弹携带数+\n     */\n    AttrBombMax = 36,\n    /**\n     * 子弹攻击+\n     */\n    BulletAttackAdd = 37,\n    /**\n     * 子弹攻击%\n     */\n    BulletAttackPer = 38,\n    /**\n     * 基于最大生命值系数的伤害%\n     */\n    HurtMaxHPPer = 39,\n    /**\n     * 基于当前生命值系数的伤害%\n     */\n    HurtCurHPPer = 40,\n    /**\n     * 核弹伤害+\n     */\n    AttrBombHurtAdd = 41,\n    /**\n     * 核弹伤害%\n     */\n    AttrBombHurtPer = 42,\n    /**\n     * 击杀\n     */\n    Kill = 43,\n    /**\n     * 伤害\n     */\n    Hurt = 44,\n}\n\n \n \n/**\n * 装备部位\n */\nexport enum EquipClass {\n    /**\n     * 无效值\n     */\n    NONE = 0,\n    /**\n     * 火力核心\n     */\n    WEAPON = 1,\n    /**\n     * 副武器\n     */\n    SUB_WEAPON = 2,\n    /**\n     * 装甲核心\n     */\n    ARMOR = 3,\n    /**\n     * 科技核心\n     */\n    TECHNIC = 4,\n}\n\n \n \n/**\n * GM命令页签\n */\nexport enum GMTabID {\n    /**\n     * 通用\n     */\n    COMMON = 0,\n    /**\n     * 战斗\n     */\n    BATTLE = 1,\n}\n\n \n \n/**\n * 道具的使用效果\n */\nexport enum ItemEffectType {\n    /**\n     * 无效果\n     */\n    NONE = 0,\n    /**\n     * 使用后消耗道具，并掉落相应的ID的随机道具，参数1为掉落ID\n     */\n    DROP = 1,\n    /**\n     * 使用后消耗道具，并掉落相应数量的金币，参数1为每个1道具对应多少个金币\n     */\n    GEN_GOLD = 2,\n    /**\n     * 使用后消耗道具，并掉落相应数量的钻石，参数1为每个1道具对应多少个钻石\n     */\n    GEN_DIAMOND = 3,\n    /**\n     * 使用后消耗道具，并掉落相应数量的经验，参数1为每个1道具对应多少经验值\n     */\n    GEN_XP = 4,\n    /**\n     * 使用后消耗道具，并掉落相应数量的体力，参数1为每个1道具对应多少体力值\n     */\n    GEN_ENERGY = 5,\n    /**\n     * 使用后消耗道具，并掉落相应数量的另一个道具，参数1为新道具ID，参数2为每个1道具对应多少个新道具\n     */\n    GEN_ITEM = 6,\n}\n\n \n \n/**\n * 道具的使用类型\n */\nexport enum ItemUseType {\n    /**\n     * 不可直接从背包来使用的道具\n     */\n    NONE = 0,\n    /**\n     * 先放入背包内，然后由玩家从背包手动选择后使用\n     */\n    MANUAL = 1,\n    /**\n     * 当进入背包时立刻触发使用，并产生效果，一般不直接占用背包格子\n     */\n    AUTO = 2,\n}\n\n \n \n/**\n * 模式类型\n */\nexport enum ModeType {\n    /**\n     * 无尽\n     */\n    ENDLESS = 0,\n    /**\n     * 剧情\n     */\n    STORY = 1,\n    /**\n     * 远征\n     */\n    EXPEDITION = 2,\n    /**\n     * 无尽PK\n     */\n    ENDLESSPK = 3,\n    /**\n     * 好友PK\n     */\n    FRIENDPK = 4,\n}\n\n \n \n/**\n * 货币类型\n */\nexport enum MoneyType {\n    /**\n     * 无效值\n     */\n    NONE = 0,\n    /**\n     * 金币\n     */\n    GOLD = 1,\n    /**\n     * 钻石\n     */\n    DIAMOND = 2,\n    /**\n     * 体力\n     */\n    POWER = 3,\n    /**\n     * 道具\n     */\n    ITEM = 4,\n}\n\n \n \n/**\n * 模式类型\n */\nexport enum PlayCycle {\n    /**\n     * 每日\n     */\n    DAY = 0,\n    /**\n     * 每周\n     */\n    WEEK = 1,\n}\n\n \n \n/**\n * 装备属性名称\n */\nexport enum PropName {\n    /**\n     * 无效值\n     */\n    NONE = 0,\n    /**\n     * 攻击力\n     */\n    HURT = 1,\n    /**\n     * 生命值\n     */\n    HP = 2,\n}\n\n \n \n/**\n * 普通(Common)、精良(Uncommon)、稀有(Rare)、史诗(Epic)、传说(Legendary)、神话(Mythic)\n */\nexport enum QualityType {\n    /**\n     * 无效值\n     */\n    NONE = 0,\n    /**\n     * 普通\n     */\n    COMMON = 1,\n    /**\n     * 精良\n     */\n    UNCOMMON = 2,\n    /**\n     * 稀有\n     */\n    RACE = 3,\n    /**\n     * 史诗\n     */\n    EPIC = 4,\n    /**\n     * 传说\n     */\n    LEGENDARY = 5,\n    /**\n     * 神话\n     */\n    MYTHIC = 6,\n}\n\n \n \n/**\n * 技能/buff条件\n */\nexport enum SkillConditionType {\n    /**\n     * 无条件\n     */\n    NONE = 0,\n}\n\n \n \n/**\n * 目标类型\n */\nexport enum TargetType {\n    /**\n     * 自己\n     */\n    Self = 0,\n    /**\n     * 自机\n     */\n    Main = 1,\n    /**\n     * 玩家方\n     */\n    MainFriendly = 2,\n    /**\n     * 全部敌方\n     */\n    Enemy = 3,\n    /**\n     * Boss敌方\n     */\n    BossEnemy = 4,\n    /**\n     * 普通敌方\n     */\n    NormalEnemy = 5,\n}\n\n \n \nexport enum TaskClass {\n    /**\n     * 日常任务\n     */\n    DAILY_TASK = 1,\n    /**\n     * 周常任务\n     */\n    WEEKLY_TASK = 2,\n    /**\n     * 挑战任务\n     */\n    CHALLENGE = 3,\n    /**\n     * 成就\n     */\n    ACHIEVEMENT = 4,\n    /**\n     * 活动\n     */\n    ACTIVITY = 5,\n}\n\n \n \nexport enum TaskCondType {\n    /**\n     * 可以不填，默认无条件限制\n     */\n    NONE = 0,\n    /**\n     * 等级\n     */\n    LEVEL = 1,\n    /**\n     * 关卡\n     */\n    STAGE = 2,\n    /**\n     * 星级\n     */\n    STAR = 3,\n    /**\n     * 战力\n     */\n    FORCE = 4,\n}\n\n \n \nexport enum TaskObjectType {\n    /**\n     * 通关或参与某模型大类达到指定次数/模式ID/次数\n     */\n    PLAYER_MODE_TIMES = 1,\n    /**\n     * 通关或参与某指定关卡达到指定次数/关卡ID/次数\n     */\n    PLAYER_STAGE_TIMES = 2,\n    /**\n     * 消灭指定类型的怪物数量达到目标/怪物类型/数量\n     */\n    KILL_MONSTER_CLASS_COUNT = 3,\n    /**\n     * 商城指定抽奖操作达到指定次数/抽奖ID/次数\n     */\n    LOTTERY_TIMES = 4,\n    /**\n     * 领取挂机奖励达到指定次数/次数\n     */\n    AFK_TIMES = 5,\n    /**\n     * 充值达到指定金额/金额\n     */\n    CHARGE_AMOUNT = 6,\n    /**\n     * 购买指定商品（包括体力等）达到指定数量/商品ID/数量\n     */\n    BUY_ITEM_COUNT = 7,\n    /**\n     * 看广告达到指定次数/次数\n     */\n    WATCH_AD_TIMES = 8,\n    /**\n     * 累计登录达到指定天数/天数\n     */\n    LOGIN_DAYS = 9,\n    /**\n     * 玩家达到指定等级\n     */\n    ROLE_LEVEL = 10,\n    /**\n     * 消耗金币达到指定数额\n     */\n    CONSOME_GOLD = 11,\n    /**\n     * 消耗钻石达到指定数额\n     */\n    CONSOME_DIAMOND = 12,\n    /**\n     * 消耗体力达到指定数额\n     */\n    CONSOME_ENERGY = 13,\n    /**\n     * 消耗道具达到指定数额\n     */\n    CONSOME_ITEM = 14,\n    /**\n     * 拥有指定品质的装备达到指定数量\n     */\n    EQUIP_QUALITY = 15,\n    /**\n     * 拥有指定等级的装备达到指定数量\n     */\n    EQUIP_LEVEL = 16,\n    /**\n     * 执行装备合成达到指定次数\n     */\n    EQUIP_COMB_TIMES = 17,\n    /**\n     * 执行装备升级达到指定次数\n     */\n    EQUIP_UNDRAGE_TIMES = 18,\n    /**\n     * 公会捐献达到指定次数\n     */\n    GUILD_DONATE_TIMES = 19,\n    /**\n     * 拥有战机（已解锁）达到指定数量\n     */\n    FIGHTER_UNLOCK_COUNT = 20,\n    /**\n     * 所有已解锁战机星级累计到指定数量\n     */\n    FIGHTER_STAR_TOTAL = 21,\n    /**\n     * 角色战力达到指定数值\n     */\n    ROLE_FORCE = 22,\n    /**\n     * 消耗指定金额的货币获得奖励\n     */\n    SPECIFY_BUG_CONSUME = 23,\n    /**\n     * 使用特定道具达到指定次数\n     */\n    USE_ITEM_TIMES = 24,\n    /**\n     * 执行签到操作\n     */\n    SIGN_IN = 25,\n}\n\n \n \nexport enum TaskPeriodType {\n    /**\n     * 单次完成，不重置， 默认，可不填\n     */\n    SINGLE = 0,\n    /**\n     * 每日 0时重置\n     */\n    DAILY = 1,\n    /**\n     * 每周 周一0时重置\n     */\n    WEEKLY = 2,\n    /**\n     * 每月 1日0时重置\n     */\n    MONTHLY = 3,\n}\n\n \n\n\n\n\n\nexport class ApplyBuff {\n\n    constructor(_json_: any) {\n        if (_json_.target === undefined) { throw new Error() }\n        this.target = _json_.target\n        if (_json_.buffID === undefined) { throw new Error() }\n        this.buffID = _json_.buffID\n    }\n\n    readonly target: TargetType\n    readonly buffID: number\n\n    resolve(tables:Tables) {\n        \n        \n    }\n}\n\n\n\n\nexport namespace builtin {\nexport class vector2 {\n\n    constructor(_json_: any) {\n        if (_json_.x === undefined) { throw new Error() }\n        this.x = _json_.x\n        if (_json_.y === undefined) { throw new Error() }\n        this.y = _json_.y\n    }\n\n    readonly x: number\n    readonly y: number\n\n    resolve(tables:Tables) {\n        \n        \n    }\n}\n\n}\n\n\nexport namespace builtin {\nexport class vector3 {\n\n    constructor(_json_: any) {\n        if (_json_.x === undefined) { throw new Error() }\n        this.x = _json_.x\n        if (_json_.y === undefined) { throw new Error() }\n        this.y = _json_.y\n        if (_json_.z === undefined) { throw new Error() }\n        this.z = _json_.z\n    }\n\n    readonly x: number\n    readonly y: number\n    readonly z: number\n\n    resolve(tables:Tables) {\n        \n        \n        \n    }\n}\n\n}\n\n\nexport namespace builtin {\nexport class vector4 {\n\n    constructor(_json_: any) {\n        if (_json_.x === undefined) { throw new Error() }\n        this.x = _json_.x\n        if (_json_.y === undefined) { throw new Error() }\n        this.y = _json_.y\n        if (_json_.z === undefined) { throw new Error() }\n        this.z = _json_.z\n        if (_json_.w === undefined) { throw new Error() }\n        this.w = _json_.w\n    }\n\n    readonly x: number\n    readonly y: number\n    readonly z: number\n    readonly w: number\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n    }\n}\n\n}\n\n\n\nexport class ConParam {\n\n    constructor(_json_: any) {\n        if (_json_.con === undefined) { throw new Error() }\n        this.con = _json_.con\n        if (_json_.param === undefined) { throw new Error() }\n        this.param = _json_.param\n    }\n\n    readonly con: number\n    readonly param: number\n\n    resolve(tables:Tables) {\n        \n        \n    }\n}\n\n\n\n\n\n/**\n * 消耗的材料\n */\nexport class ConsumeItem {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.num === undefined) { throw new Error() }\n        this.num = _json_.num\n    }\n\n    readonly id: number\n    readonly num: number\n\n    resolve(tables:Tables) {\n        \n        \n    }\n}\n\n\n\n\n\nexport class ConsumeMoney {\n\n    constructor(_json_: any) {\n        if (_json_.type === undefined) { throw new Error() }\n        this.type = _json_.type\n        if (_json_.num === undefined) { throw new Error() }\n        this.num = _json_.num\n    }\n\n    /**\n     * 货币类型\n     */\n    readonly type: MoneyType\n    /**\n     * 货币数量\n     */\n    readonly num: number\n\n    resolve(tables:Tables) {\n        \n        \n    }\n}\n\n\n\n\n\nexport class EffectParam {\n\n    constructor(_json_: any) {\n        if (_json_.type === undefined) { throw new Error() }\n        this.type = _json_.type\n        if (_json_.target === undefined) { throw new Error() }\n        this.target = _json_.target\n        if (_json_.param === undefined) { throw new Error() }\n        { this.param = []; for(let _ele0 of _json_.param) { let _e0; _e0 = _ele0; this.param.push(_e0);}}\n    }\n\n    readonly type: EffectType\n    readonly target: TargetType\n    readonly param: number[]\n\n    resolve(tables:Tables) {\n        \n        \n        \n    }\n}\n\n\n\n\n\n/**\n * 装备属性\n */\nexport class EquipProp {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.value === undefined) { throw new Error() }\n        this.value = _json_.value\n    }\n\n    readonly id: PropName\n    readonly value: number\n\n    resolve(tables:Tables) {\n        \n        \n    }\n}\n\n\n\n\n\nexport class GM {\n\n    constructor(_json_: any) {\n        if (_json_.tabID === undefined) { throw new Error() }\n        this.tabID = _json_.tabID\n        if (_json_.tabName === undefined) { throw new Error() }\n        this.tabName = _json_.tabName\n        if (_json_.name === undefined) { throw new Error() }\n        this.name = _json_.name\n        if (_json_.cmd === undefined) { throw new Error() }\n        this.cmd = _json_.cmd\n        if (_json_.desc === undefined) { throw new Error() }\n        this.desc = _json_.desc\n    }\n\n    /**\n     * 页签ID\n     */\n    readonly tabID: GMTabID\n    /**\n     * 页签名称\n     */\n    readonly tabName: string\n    /**\n     * 按钮名称\n     */\n    readonly name: string\n    /**\n     * 命令\n     */\n    readonly cmd: string\n    /**\n     * 描述\n     */\n    readonly desc: string\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\n/**\n * 战机效果\n */\nexport class PlaneEffect {\n\n    constructor(_json_: any) {\n        if (_json_.effect_id === undefined) { throw new Error() }\n        this.effectId = _json_.effect_id\n    }\n\n    readonly effectId: number\n\n    resolve(tables:Tables) {\n        \n    }\n}\n\n\n\n\n\n/**\n * 升星材料\n */\nexport class PlaneMaterial {\n\n    constructor(_json_: any) {\n        if (_json_.material_id === undefined) { throw new Error() }\n        this.materialId = _json_.material_id\n        if (_json_.material_count === undefined) { throw new Error() }\n        this.materialCount = _json_.material_count\n    }\n\n    readonly materialId: number\n    readonly materialCount: number\n\n    resolve(tables:Tables) {\n        \n        \n    }\n}\n\n\n\n\n\n/**\n * 战机属性\n */\nexport class PlaneProperty {\n\n    constructor(_json_: any) {\n        if (_json_.prop_type === undefined) { throw new Error() }\n        this.propType = _json_.prop_type\n        if (_json_.prop_value === undefined) { throw new Error() }\n        this.propValue = _json_.prop_value\n    }\n\n    readonly propType: PropName\n    readonly propValue: number\n\n    resolve(tables:Tables) {\n        \n        \n    }\n}\n\n\n\n\n\n/**\n * 属性增幅\n */\nexport class PropInc {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.inc === undefined) { throw new Error() }\n        this.inc = _json_.inc\n    }\n\n    readonly id: PropName\n    /**\n     * 万分比\n     */\n    readonly inc: number\n\n    resolve(tables:Tables) {\n        \n        \n    }\n}\n\n\n\n\n\n/**\n * 随机策略\n */\nexport class randStrategy {\n\n    constructor(_json_: any) {\n        if (_json_.ID === undefined) { throw new Error() }\n        this.ID = _json_.ID\n        if (_json_.Weight === undefined) { throw new Error() }\n        this.Weight = _json_.Weight\n    }\n\n    /**\n     * 随机策略ID\n     */\n    readonly ID: number\n    /**\n     * ID的权重\n     */\n    readonly Weight: number\n\n    resolve(tables:Tables) {\n        \n        \n    }\n}\n\n\n\n\n\nexport class RatingParam {\n\n    constructor(_json_: any) {\n        if (_json_.rating === undefined) { throw new Error() }\n        this.rating = _json_.rating\n        if (_json_.param === undefined) { throw new Error() }\n        this.param = _json_.param\n    }\n\n    readonly rating: number\n    readonly param: number\n\n    resolve(tables:Tables) {\n        \n        \n    }\n}\n\n\n\n\n\nexport class ResBoss {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.bId === undefined) { throw new Error() }\n        this.bId = _json_.bId\n        if (_json_.sId === undefined) { throw new Error() }\n        this.sId = _json_.sId\n        if (_json_.app === undefined) { throw new Error() }\n        this.app = _json_.app\n        if (_json_.ta === undefined) { throw new Error() }\n        this.ta = _json_.ta\n        if (_json_.ft === undefined) { throw new Error() }\n        this.ft = _json_.ft\n        if (_json_.leave === undefined) { throw new Error() }\n        this.leave = _json_.leave\n        if (_json_.exp === undefined) { throw new Error() }\n        this.exp = _json_.exp\n        if (_json_.rid === undefined) { throw new Error() }\n        this.rid = _json_.rid\n        if (_json_.sk === undefined) { throw new Error() }\n        this.sk = _json_.sk\n        if (_json_.blp === undefined) { throw new Error() }\n        this.blp = _json_.blp\n        if (_json_.us === undefined) { throw new Error() }\n        this.us = _json_.us\n        if (_json_.ua === undefined) { throw new Error() }\n        this.ua = _json_.ua\n        if (_json_.va === undefined) { throw new Error() }\n        this.va = _json_.va\n        if (_json_.sv === undefined) { throw new Error() }\n        this.sv = _json_.sv\n        if (_json_.fl === undefined) { throw new Error() }\n        this.fl = _json_.fl\n        if (_json_.loot === undefined) { throw new Error() }\n        this.loot = _json_.loot\n        if (_json_.adsorb === undefined) { throw new Error() }\n        this.adsorb = _json_.adsorb\n        if (_json_.lp0 === undefined) { throw new Error() }\n        this.lp0 = _json_.lp0\n        if (_json_.lp1 === undefined) { throw new Error() }\n        this.lp1 = _json_.lp1\n        if (_json_.dh === undefined) { throw new Error() }\n        this.dh = _json_.dh\n        if (_json_.atk === undefined) { throw new Error() }\n        this.atk = _json_.atk\n        if (_json_.col === undefined) { throw new Error() }\n        this.col = _json_.col\n        if (_json_.tway === undefined) { throw new Error() }\n        this.tway = _json_.tway\n        if (_json_.way === undefined) { throw new Error() }\n        this.way = _json_.way\n        if (_json_.wi === undefined) { throw new Error() }\n        this.wi = _json_.wi\n        if (_json_.sp === undefined) { throw new Error() }\n        this.sp = _json_.sp\n        if (_json_.ai === undefined) { throw new Error() }\n        this.ai = _json_.ai\n        if (_json_.ra === undefined) { throw new Error() }\n        this.ra = _json_.ra\n        if (_json_.a0 === undefined) { throw new Error() }\n        this.a0 = _json_.a0\n        if (_json_.a1 === undefined) { throw new Error() }\n        this.a1 = _json_.a1\n        if (_json_.a2 === undefined) { throw new Error() }\n        this.a2 = _json_.a2\n        if (_json_.a3 === undefined) { throw new Error() }\n        this.a3 = _json_.a3\n        if (_json_.a4 === undefined) { throw new Error() }\n        this.a4 = _json_.a4\n        if (_json_.a5 === undefined) { throw new Error() }\n        this.a5 = _json_.a5\n        if (_json_.a6 === undefined) { throw new Error() }\n        this.a6 = _json_.a6\n        if (_json_.a7 === undefined) { throw new Error() }\n        this.a7 = _json_.a7\n        if (_json_.a8 === undefined) { throw new Error() }\n        this.a8 = _json_.a8\n        if (_json_.a9 === undefined) { throw new Error() }\n        this.a9 = _json_.a9\n        if (_json_.a10 === undefined) { throw new Error() }\n        this.a10 = _json_.a10\n        if (_json_.a11 === undefined) { throw new Error() }\n        this.a11 = _json_.a11\n        if (_json_.a12 === undefined) { throw new Error() }\n        this.a12 = _json_.a12\n        if (_json_.a13 === undefined) { throw new Error() }\n        this.a13 = _json_.a13\n        if (_json_.a14 === undefined) { throw new Error() }\n        this.a14 = _json_.a14\n        if (_json_.a15 === undefined) { throw new Error() }\n        this.a15 = _json_.a15\n        if (_json_.a16 === undefined) { throw new Error() }\n        this.a16 = _json_.a16\n        if (_json_.a17 === undefined) { throw new Error() }\n        this.a17 = _json_.a17\n        if (_json_.a18 === undefined) { throw new Error() }\n        this.a18 = _json_.a18\n        if (_json_.a19 === undefined) { throw new Error() }\n        this.a19 = _json_.a19\n        if (_json_.a20 === undefined) { throw new Error() }\n        this.a20 = _json_.a20\n        if (_json_.a21 === undefined) { throw new Error() }\n        this.a21 = _json_.a21\n        if (_json_.a22 === undefined) { throw new Error() }\n        this.a22 = _json_.a22\n        if (_json_.a100 === undefined) { throw new Error() }\n        this.a100 = _json_.a100\n        if (_json_.a101 === undefined) { throw new Error() }\n        this.a101 = _json_.a101\n    }\n\n    /**\n     * id\n     */\n    readonly id: number\n    /**\n     * 飞机id\n     */\n    readonly bId: number\n    /**\n     * 子类型\n     */\n    readonly sId: number\n    /**\n     * 出场参数\n     */\n    readonly app: string\n    /**\n     * inAudio\n     */\n    readonly ta: string\n    /**\n     * 死亡掉落延迟\n     */\n    readonly ft: number\n    /**\n     * leave\n     */\n    readonly leave: number\n    /**\n     * exp\n     */\n    readonly exp: number\n    /**\n     * rid\n     */\n    readonly rid: string\n    /**\n     * 爆炸震动参数\n     */\n    readonly sk: string\n    /**\n     * 爆炸参数\n     */\n    readonly blp: string\n    /**\n     * us\n     */\n    readonly us: string\n    /**\n     * ua\n     */\n    readonly ua: string\n    /**\n     * va\n     */\n    readonly va: string\n    /**\n     * sv\n     */\n    readonly sv: string\n    /**\n     * fl\n     */\n    readonly fl: string\n    /**\n     * loot\n     */\n    readonly loot: string\n    /**\n     * adsorb\n     */\n    readonly adsorb: string\n    /**\n     * lp0\n     */\n    readonly lp0: string\n    /**\n     * lp1\n     */\n    readonly lp1: string\n    /**\n     * dh\n     */\n    readonly dh: string\n    /**\n     * atk\n     */\n    readonly atk: number\n    /**\n     * col\n     */\n    readonly col: number\n    /**\n     * tway\n     */\n    readonly tway: string\n    /**\n     * way\n     */\n    readonly way: string\n    /**\n     * wi\n     */\n    readonly wi: string\n    /**\n     * sp\n     */\n    readonly sp: string\n    /**\n     * ai\n     */\n    readonly ai: string\n    /**\n     * ra\n     */\n    readonly ra: string\n    /**\n     * a0\n     */\n    readonly a0: string\n    /**\n     * a1\n     */\n    readonly a1: string\n    /**\n     * a2\n     */\n    readonly a2: string\n    /**\n     * a3\n     */\n    readonly a3: string\n    /**\n     * a4\n     */\n    readonly a4: string\n    /**\n     * a5\n     */\n    readonly a5: string\n    /**\n     * a6\n     */\n    readonly a6: string\n    /**\n     * a7\n     */\n    readonly a7: string\n    /**\n     * a8\n     */\n    readonly a8: string\n    /**\n     * a9\n     */\n    readonly a9: string\n    /**\n     * a10\n     */\n    readonly a10: string\n    /**\n     * a11\n     */\n    readonly a11: string\n    /**\n     * a12\n     */\n    readonly a12: string\n    /**\n     * a13\n     */\n    readonly a13: string\n    /**\n     * a14\n     */\n    readonly a14: string\n    /**\n     * a15\n     */\n    readonly a15: string\n    /**\n     * a16\n     */\n    readonly a16: string\n    /**\n     * a17\n     */\n    readonly a17: string\n    /**\n     * a18\n     */\n    readonly a18: string\n    /**\n     * a19\n     */\n    readonly a19: string\n    /**\n     * a20\n     */\n    readonly a20: string\n    /**\n     * a21\n     */\n    readonly a21: string\n    /**\n     * a22\n     */\n    readonly a22: string\n    /**\n     * a100\n     */\n    readonly a100: string\n    /**\n     * a101\n     */\n    readonly a101: string\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class ResBuffer {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.buffType === undefined) { throw new Error() }\n        this.buffType = _json_.buffType\n        if (_json_.duration === undefined) { throw new Error() }\n        this.duration = _json_.duration\n        if (_json_.durationBonus === undefined) { throw new Error() }\n        this.durationBonus = _json_.durationBonus\n        if (_json_.maxStack === undefined) { throw new Error() }\n        this.maxStack = _json_.maxStack\n        if (_json_.refreshType === undefined) { throw new Error() }\n        this.refreshType = _json_.refreshType\n        if (_json_.cycle === undefined) { throw new Error() }\n        this.cycle = _json_.cycle\n        if (_json_.cycleTimes === undefined) { throw new Error() }\n        this.cycleTimes = _json_.cycleTimes\n        if (_json_.effects === undefined) { throw new Error() }\n        { this.effects = []; for(let _ele0 of _json_.effects) { let _e0; _e0 = new EffectParam(_ele0); this.effects.push(_e0);}}\n        if (_json_.conditionID === undefined) { throw new Error() }\n        this.conditionID = _json_.conditionID\n    }\n\n    /**\n     * ID\n     */\n    readonly id: number\n    /**\n     * 类别\n     */\n    readonly buffType: BuffType\n    /**\n     * 持续时间\n     */\n    readonly duration: number\n    /**\n     * 持续时间加成\n     */\n    readonly durationBonus: number\n    /**\n     * 最大叠加次数\n     */\n    readonly maxStack: number\n    /**\n     * 叠加刷新策略\n     */\n    readonly refreshType: boolean\n    /**\n     * 周期\n     */\n    readonly cycle: number\n    /**\n     * 周期计数\n     */\n    readonly cycleTimes: number\n    readonly effects: EffectParam[]\n    /**\n     * 禁用条件\n     */\n    readonly conditionID: number\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class ResBullet {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.name === undefined) { throw new Error() }\n        this.name = _json_.name\n        if (_json_.source === undefined) { throw new Error() }\n        this.source = _json_.source\n        if (_json_.type === undefined) { throw new Error() }\n        this.type = _json_.type\n        if (_json_.prefab === undefined) { throw new Error() }\n        this.prefab = _json_.prefab\n        if (_json_.attack_coefficient === undefined) { throw new Error() }\n        this.attackCoefficient = _json_.attack_coefficient\n    }\n\n    /**\n     * ID\n     */\n    readonly id: number\n    /**\n     * 名称\n     */\n    readonly name: string\n    /**\n     * 子弹来源\n     */\n    readonly source: BulletSourceType\n    /**\n     * 子弹类型\n     */\n    readonly type: BulletType\n    /**\n     * 子弹Prefab\n     */\n    readonly prefab: string\n    /**\n     * 攻击转换系数%\n     */\n    readonly attackCoefficient: number\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class ResChapter {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.levelCount === undefined) { throw new Error() }\n        this.levelCount = _json_.levelCount\n        if (_json_.levelGroupCount === undefined) { throw new Error() }\n        this.levelGroupCount = _json_.levelGroupCount\n        if (_json_.strategy === undefined) { throw new Error() }\n        this.strategy = _json_.strategy\n        if (_json_.damageBonus === undefined) { throw new Error() }\n        this.damageBonus = _json_.damageBonus\n        if (_json_.lifeBounus === undefined) { throw new Error() }\n        this.lifeBounus = _json_.lifeBounus\n        if (_json_.strategyList === undefined) { throw new Error() }\n        { this.strategyList = []; for(let _ele0 of _json_.strategyList) { let _e0; _e0 = new randStrategy(_ele0); this.strategyList.push(_e0);}}\n    }\n\n    /**\n     * 章节ID\n     */\n    readonly id: number\n    /**\n     * 章节关卡数量\n     */\n    readonly levelCount: number\n    /**\n     * 章节关卡组数量\n     */\n    readonly levelGroupCount: number\n    /**\n     * 1=随机<br/>2=随机不重复<br/>3=顺序重复\n     */\n    readonly strategy: number\n    /**\n     * 章节伤害加成\n     */\n    readonly damageBonus: number\n    /**\n     * 章节生命加成\n     */\n    readonly lifeBounus: number\n    readonly strategyList: randStrategy[]\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        for (let _e of this.strategyList) { _e?.resolve(tables); }\n    }\n}\n\n\n\n\n\nexport class ResEffect {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.name === undefined) { throw new Error() }\n        this.name = _json_.name\n        if (_json_.description === undefined) { throw new Error() }\n        this.description = _json_.description\n        if (_json_.icon === undefined) { throw new Error() }\n        this.icon = _json_.icon\n        if (_json_.effect_type === undefined) { throw new Error() }\n        this.effectType = _json_.effect_type\n        if (_json_.effect_value === undefined) { throw new Error() }\n        this.effectValue = _json_.effect_value\n        if (_json_.effect_params === undefined) { throw new Error() }\n        this.effectParams = _json_.effect_params\n    }\n\n    /**\n     * 效果ID\n     */\n    readonly id: number\n    /**\n     * 效果名称\n     */\n    readonly name: string\n    /**\n     * 效果描述\n     */\n    readonly description: string\n    /**\n     * 效果图标\n     */\n    readonly icon: string\n    /**\n     * 效果类型\n     */\n    readonly effectType: number\n    /**\n     * 效果数值\n     */\n    readonly effectValue: number\n    /**\n     * 效果参数\n     */\n    readonly effectParams: string\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class ResEnemy {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.uiId === undefined) { throw new Error() }\n        this.uiId = _json_.uiId\n        if (_json_.atk === undefined) { throw new Error() }\n        this.atk = _json_.atk\n        if (_json_.hp === undefined) { throw new Error() }\n        this.hp = _json_.hp\n        if (_json_.collideLevel === undefined) { throw new Error() }\n        this.collideLevel = _json_.collideLevel\n        if (_json_.turn === undefined) { throw new Error() }\n        this.turn = _json_.turn\n        if (_json_.hpShow === undefined) { throw new Error() }\n        this.hpShow = _json_.hpShow\n        if (_json_.collideAttack === undefined) { throw new Error() }\n        this.collideAttack = _json_.collideAttack\n        if (_json_.bCollideDead === undefined) { throw new Error() }\n        this.bCollideDead = _json_.bCollideDead\n        if (_json_.bMoveAttack === undefined) { throw new Error() }\n        this.bMoveAttack = _json_.bMoveAttack\n        if (_json_.bStayAttack === undefined) { throw new Error() }\n        this.bStayAttack = _json_.bStayAttack\n        if (_json_.attackInterval === undefined) { throw new Error() }\n        this.attackInterval = _json_.attackInterval\n        if (_json_.attackNum === undefined) { throw new Error() }\n        this.attackNum = _json_.attackNum\n        if (_json_.attackData === undefined) { throw new Error() }\n        this.attackData = _json_.attackData\n        if (_json_.param === undefined) { throw new Error() }\n        this.param = _json_.param\n        if (_json_.dieShoot === undefined) { throw new Error() }\n        this.dieShoot = _json_.dieShoot\n        if (_json_.dieBullet === undefined) { throw new Error() }\n        this.dieBullet = _json_.dieBullet\n    }\n\n    /**\n     * id\n     */\n    readonly id: number\n    /**\n     * 敌机的显示id\n     */\n    readonly uiId: number\n    /**\n     * 攻击\n     */\n    readonly atk: number\n    /**\n     * 血量\n     */\n    readonly hp: number\n    /**\n     * 碰撞等级\n     */\n    readonly collideLevel: number\n    /**\n     * 是否改变方向\n     */\n    readonly turn: number\n    /**\n     * 是否显示血条\n     */\n    readonly hpShow: number\n    /**\n     * 碰撞伤害值\n     */\n    readonly collideAttack: number\n    /**\n     * 碰撞后是否死亡\n     */\n    readonly bCollideDead: number\n    /**\n     * 移动时是否攻击\n     */\n    readonly bMoveAttack: number\n    /**\n     * 静止时是否攻击\n     */\n    readonly bStayAttack: number\n    /**\n     * 攻击间隔时间\n     */\n    readonly attackInterval: number\n    /**\n     * 攻击次数\n     */\n    readonly attackNum: number\n    /**\n     * 攻击点位置数据(x,y;间隔,子弹id,子弹数量,子弹间隔,子弹攻击力百分比(100为1倍);)\n     */\n    readonly attackData: string\n    /**\n     * 自定义参数\n     */\n    readonly param: string\n    /**\n     * 死亡时发射的子弹数据\n     */\n    readonly dieShoot: string\n    /**\n     * 死亡时是否发射子弹\n     */\n    readonly dieBullet: number\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class ResEquip {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.name === undefined) { throw new Error() }\n        this.name = _json_.name\n        if (_json_.icon === undefined) { throw new Error() }\n        this.icon = _json_.icon\n        if (_json_.quality === undefined) { throw new Error() }\n        this.quality = _json_.quality\n        if (_json_.quality_sub === undefined) { throw new Error() }\n        this.qualitySub = _json_.quality_sub\n        if (_json_.equip_class === undefined) { throw new Error() }\n        this.equipClass = _json_.equip_class\n        if (_json_.props === undefined) { throw new Error() }\n        { this.props = []; for(let _ele0 of _json_.props) { let _e0; _e0 = new EquipProp(_ele0); this.props.push(_e0);}}\n        if (_json_.consume_items === undefined) { throw new Error() }\n        { this.consumeItems = []; for(let _ele0 of _json_.consume_items) { let _e0; _e0 = new ConsumeItem(_ele0); this.consumeItems.push(_e0);}}\n    }\n\n    /**\n     * ID\n     */\n    readonly id: number\n    /**\n     * 名称\n     */\n    readonly name: string\n    /**\n     * 图标\n     */\n    readonly icon: string\n    /**\n     * 品质\n     */\n    readonly quality: QualityType\n    /**\n     * 品质子等级\n     */\n    readonly qualitySub: number\n    /**\n     * 装备部位\n     */\n    readonly equipClass: EquipClass\n    /**\n     * 属性\n     */\n    readonly props: EquipProp[]\n    /**\n     * 消耗材料\n     */\n    readonly consumeItems: ConsumeItem[]\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        for (let _e of this.props) { _e?.resolve(tables); }\n        for (let _e of this.consumeItems) { _e?.resolve(tables); }\n    }\n}\n\n\n\n\n\nexport class ResEquipUpgrade {\n\n    constructor(_json_: any) {\n        if (_json_.equip_class === undefined) { throw new Error() }\n        this.equipClass = _json_.equip_class\n        if (_json_.level_from === undefined) { throw new Error() }\n        this.levelFrom = _json_.level_from\n        if (_json_.level_to === undefined) { throw new Error() }\n        this.levelTo = _json_.level_to\n        if (_json_.prop_inc === undefined) { throw new Error() }\n        { this.propInc = []; for(let _ele0 of _json_.prop_inc) { let _e0; _e0 = new PropInc(_ele0); this.propInc.push(_e0);}}\n        if (_json_.consume_money === undefined) { throw new Error() }\n        this.consumeMoney = new ConsumeMoney(_json_.consume_money)\n        if (_json_.consume_items === undefined) { throw new Error() }\n        { this.consumeItems = []; for(let _ele0 of _json_.consume_items) { let _e0; _e0 = new ConsumeItem(_ele0); this.consumeItems.push(_e0);}}\n    }\n\n    /**\n     * 部位\n     */\n    readonly equipClass: EquipClass\n    /**\n     * 等级下限\n     */\n    readonly levelFrom: number\n    /**\n     * 等级上限\n     */\n    readonly levelTo: number\n    /**\n     * 属性增幅\n     */\n    readonly propInc: PropInc[]\n    /**\n     * 消耗货币\n     */\n    readonly consumeMoney: ConsumeMoney\n    /**\n     * 消耗材料\n     */\n    readonly consumeItems: ConsumeItem[]\n\n    resolve(tables:Tables) {\n        \n        \n        \n        for (let _e of this.propInc) { _e?.resolve(tables); }\n        this.consumeMoney?.resolve(tables);\n        for (let _e of this.consumeItems) { _e?.resolve(tables); }\n    }\n}\n\n\n\n\n\nexport class ResGameMode {\n\n    constructor(_json_: any) {\n        if (_json_.ID === undefined) { throw new Error() }\n        this.ID = _json_.ID\n        if (_json_.modeType === undefined) { throw new Error() }\n        this.modeType = _json_.modeType\n        if (_json_.chapterID === undefined) { throw new Error() }\n        this.chapterID = _json_.chapterID\n        if (_json_.order === undefined) { throw new Error() }\n        this.order = _json_.order\n        if (_json_.resourceID === undefined) { throw new Error() }\n        this.resourceID = _json_.resourceID\n        if (_json_.description === undefined) { throw new Error() }\n        this.description = _json_.description\n        if (_json_.conList === undefined) { throw new Error() }\n        { this.conList = []; for(let _ele0 of _json_.conList) { let _e0; _e0 = new ConParam(_ele0); this.conList.push(_e0);}}\n        if (_json_.cycle === undefined) { throw new Error() }\n        this.cycle = _json_.cycle\n        if (_json_.times === undefined) { throw new Error() }\n        this.times = _json_.times\n        if (_json_.monType === undefined) { throw new Error() }\n        this.monType = _json_.monType\n        if (_json_.costParam1 === undefined) { throw new Error() }\n        this.costParam1 = _json_.costParam1\n        if (_json_.costParam2 === undefined) { throw new Error() }\n        this.costParam2 = _json_.costParam2\n        if (_json_.rebirthTimes === undefined) { throw new Error() }\n        this.rebirthTimes = _json_.rebirthTimes\n        if (_json_.rebirthCost === undefined) { throw new Error() }\n        this.rebirthCost = _json_.rebirthCost\n        if (_json_.power === undefined) { throw new Error() }\n        this.power = _json_.power\n        if (_json_.rogueID === undefined) { throw new Error() }\n        this.rogueID = _json_.rogueID\n        if (_json_.LevelLimit === undefined) { throw new Error() }\n        this.LevelLimit = _json_.LevelLimit\n        if (_json_.rogueFirst === undefined) { throw new Error() }\n        this.rogueFirst = _json_.rogueFirst\n        if (_json_.sweepLimit === undefined) { throw new Error() }\n        this.sweepLimit = _json_.sweepLimit\n        if (_json_.rewardID1 === undefined) { throw new Error() }\n        this.rewardID1 = _json_.rewardID1\n        if (_json_.rewardID2 === undefined) { throw new Error() }\n        this.rewardID2 = _json_.rewardID2\n        if (_json_.ratingList === undefined) { throw new Error() }\n        { this.ratingList = []; for(let _ele0 of _json_.ratingList) { let _e0; _e0 = new RatingParam(_ele0); this.ratingList.push(_e0);}}\n    }\n\n    /**\n     * ID\n     */\n    readonly ID: number\n    /**\n     * 模式类型\n     */\n    readonly modeType: ModeType\n    /**\n     * 章节ID\n     */\n    readonly chapterID: number\n    /**\n     * 排序\n     */\n    readonly order: number\n    /**\n     * 入口资源\n     */\n    readonly resourceID: number\n    /**\n     * 文本介绍\n     */\n    readonly description: string\n    readonly conList: ConParam[]\n    /**\n     * 进入周期\n     */\n    readonly cycle: PlayCycle\n    /**\n     * 进入次数\n     */\n    readonly times: number\n    /**\n     * 消耗类型\n     */\n    readonly monType: MoneyType\n    /**\n     * 消耗参数1\n     */\n    readonly costParam1: number\n    /**\n     * 消耗参数2\n     */\n    readonly costParam2: number\n    /**\n     * 复活次数\n     */\n    readonly rebirthTimes: number\n    /**\n     * 复活消耗\n     */\n    readonly rebirthCost: number\n    /**\n     * 战力评估\n     */\n    readonly power: number\n    /**\n     * 肉鸽组\n     */\n    readonly rogueID: number\n    /**\n     * 局内等级上限\n     */\n    readonly LevelLimit: number\n    /**\n     * 初始肉鸽选择\n     */\n    readonly rogueFirst: number\n    /**\n     * 扫荡次数\n     */\n    readonly sweepLimit: number\n    /**\n     * 奖励ID1\n     */\n    readonly rewardID1: number\n    /**\n     * 奖励ID2\n     */\n    readonly rewardID2: number\n    readonly ratingList: RatingParam[]\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class ResGlobalAttr {\n\n    constructor(_json_: any) {\n        if (_json_.GoldProducion === undefined) { throw new Error() }\n        this.GoldProducion = _json_.GoldProducion\n        if (_json_.MaxEnergy === undefined) { throw new Error() }\n        this.MaxEnergy = _json_.MaxEnergy\n        if (_json_.EnergyRecoverInterval === undefined) { throw new Error() }\n        this.EnergyRecoverInterval = _json_.EnergyRecoverInterval\n        if (_json_.EnergyRecoverValue === undefined) { throw new Error() }\n        this.EnergyRecoverValue = _json_.EnergyRecoverValue\n        if (_json_.ItemPickUpRadius === undefined) { throw new Error() }\n        this.ItemPickUpRadius = _json_.ItemPickUpRadius\n        if (_json_.PostHitProtection === undefined) { throw new Error() }\n        this.PostHitProtection = _json_.PostHitProtection\n    }\n\n    /**\n     * 每回合发放的金币\n     */\n    readonly GoldProducion: number\n    /**\n     * 体力上限值\n     */\n    readonly MaxEnergy: number\n    /**\n     * 体力恢复的间隔时间\n     */\n    readonly EnergyRecoverInterval: number\n    /**\n     * 体力恢复的值\n     */\n    readonly EnergyRecoverValue: number\n    /**\n     * 局内道具拾取距离\n     */\n    readonly ItemPickUpRadius: number\n    /**\n     * 受击保护\n     */\n    readonly PostHitProtection: number\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class ResItem {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.name === undefined) { throw new Error() }\n        this.name = _json_.name\n        if (_json_.icon === undefined) { throw new Error() }\n        this.icon = _json_.icon\n        if (_json_.quality === undefined) { throw new Error() }\n        this.quality = _json_.quality\n        if (_json_.quality_sub === undefined) { throw new Error() }\n        this.qualitySub = _json_.quality_sub\n        if (_json_.use_type === undefined) { throw new Error() }\n        this.useType = _json_.use_type\n        if (_json_.effect_id === undefined) { throw new Error() }\n        this.effectId = _json_.effect_id\n        if (_json_.effect_param1 === undefined) { throw new Error() }\n        this.effectParam1 = _json_.effect_param1\n        if (_json_.effect_param2 === undefined) { throw new Error() }\n        this.effectParam2 = _json_.effect_param2\n        if (_json_.max_stack_num === undefined) { throw new Error() }\n        this.maxStackNum = _json_.max_stack_num\n    }\n\n    /**\n     * ID\n     */\n    readonly id: number\n    /**\n     * 名称\n     */\n    readonly name: string\n    /**\n     * 图标\n     */\n    readonly icon: string\n    /**\n     * 品质\n     */\n    readonly quality: QualityType\n    /**\n     * 品质子等级\n     */\n    readonly qualitySub: number\n    /**\n     * 使用类型\n     */\n    readonly useType: ItemUseType\n    /**\n     * 效果类型\n     */\n    readonly effectId: ItemEffectType\n    /**\n     * 效果参数1\n     */\n    readonly effectParam1: number\n    /**\n     * 效果参数2\n     */\n    readonly effectParam2: number\n    /**\n     * 最大叠放数量\n     */\n    readonly maxStackNum: number\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class ResLevel {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.prefab === undefined) { throw new Error() }\n        this.prefab = _json_.prefab\n        if (_json_.forbidFire === undefined) { throw new Error() }\n        this.forbidFire = _json_.forbidFire\n        if (_json_.forbidNBomb === undefined) { throw new Error() }\n        this.forbidNBomb = _json_.forbidNBomb\n        if (_json_.forbidActSkill === undefined) { throw new Error() }\n        this.forbidActSkill = _json_.forbidActSkill\n        if (_json_.planeCollisionScaling === undefined) { throw new Error() }\n        this.planeCollisionScaling = _json_.planeCollisionScaling\n        if (_json_.levelType === undefined) { throw new Error() }\n        this.levelType = _json_.levelType\n    }\n\n    /**\n     * 关卡id\n     */\n    readonly id: number\n    /**\n     * 关卡prefab\n     */\n    readonly prefab: string\n    /**\n     * 是、否（默认值）\n     */\n    readonly forbidFire: boolean\n    /**\n     * 是、否（默认值）\n     */\n    readonly forbidNBomb: boolean\n    /**\n     * 是、否（默认值）\n     */\n    readonly forbidActSkill: boolean\n    /**\n     * 0到1（1表示正常碰撞）\n     */\n    readonly planeCollisionScaling: number\n    /**\n     * 1=常规关卡<br/>2=机关关卡<br/>3=金币关卡<br/>4=火箭关卡<br/>99=Boss关\n     */\n    readonly levelType: number\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class ResLevelGroup {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.normLevelCount === undefined) { throw new Error() }\n        this.normLevelCount = _json_.normLevelCount\n        if (_json_.normLevelST === undefined) { throw new Error() }\n        this.normLevelST = _json_.normLevelST\n        if (_json_.normSTList === undefined) { throw new Error() }\n        { this.normSTList = []; for(let _ele0 of _json_.normSTList) { let _e0; _e0 = new randStrategy(_ele0); this.normSTList.push(_e0);}}\n        if (_json_.bossLevelCount === undefined) { throw new Error() }\n        this.bossLevelCount = _json_.bossLevelCount\n        if (_json_.bossLevelST === undefined) { throw new Error() }\n        this.bossLevelST = _json_.bossLevelST\n        if (_json_.bossSTList === undefined) { throw new Error() }\n        { this.bossSTList = []; for(let _ele0 of _json_.bossSTList) { let _e0; _e0 = new randStrategy(_ele0); this.bossSTList.push(_e0);}}\n    }\n\n    /**\n     * 关卡组ID\n     */\n    readonly id: number\n    /**\n     * 常规关卡数量\n     */\n    readonly normLevelCount: number\n    /**\n     * 1=随机<br/>2=随机不重复<br/>3=顺序重复\n     */\n    readonly normLevelST: number\n    readonly normSTList: randStrategy[]\n    /**\n     * BOSS关卡数量\n     */\n    readonly bossLevelCount: number\n    /**\n     * 1=随机<br/>2=随机不重复<br/>3=顺序重复\n     */\n    readonly bossLevelST: number\n    readonly bossSTList: randStrategy[]\n\n    resolve(tables:Tables) {\n        \n        \n        \n        for (let _e of this.normSTList) { _e?.resolve(tables); }\n        \n        \n        for (let _e of this.bossSTList) { _e?.resolve(tables); }\n    }\n}\n\n\n\n\n\nexport class ResMainPlane {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.type === undefined) { throw new Error() }\n        this.type = _json_.type\n        if (_json_.body === undefined) { throw new Error() }\n        { this.body = []; for(let _ele0 of _json_.body) { let _e0; _e0 = _ele0; this.body.push(_e0);}}\n        if (_json_.transSrc === undefined) { throw new Error() }\n        { this.transSrc = []; for(let _ele0 of _json_.transSrc) { let _e0; _e0 = _ele0; this.transSrc.push(_e0);}}\n        if (_json_.transExt === undefined) { throw new Error() }\n        { this.transExt = []; for(let _ele0 of _json_.transExt) { let _e0; _e0 = _ele0; this.transExt.push(_e0);}}\n        if (_json_.zjdmtxzb === undefined) { throw new Error() }\n        { this.zjdmtxzb = []; for(let _ele0 of _json_.zjdmtxzb) { let _e0; _e0 = _ele0; this.zjdmtxzb.push(_e0);}}\n        if (_json_.transatk1 === undefined) { throw new Error() }\n        { this.transatk1 = []; for(let _ele0 of _json_.transatk1) { let _e0; _e0 = _ele0; this.transatk1.push(_e0);}}\n        if (_json_.shiftingatk1 === undefined) { throw new Error() }\n        { this.shiftingatk1 = []; for(let _ele0 of _json_.shiftingatk1) { let _e0; _e0 = _ele0; this.shiftingatk1.push(_e0);}}\n    }\n\n    /**\n     * id\n     */\n    readonly id: number\n    /**\n     * type\n     */\n    readonly type: number\n    /**\n     * 碰撞范围\n     */\n    readonly body: number[]\n    /**\n     * 变现\n     */\n    readonly transSrc: string[]\n    /**\n     * 变形参数\n     */\n    readonly transExt: string[]\n    /**\n     * 火焰位置\n     */\n    readonly zjdmtxzb: number[]\n    /**\n     * transatk1\n     */\n    readonly transatk1: number[]\n    /**\n     * shiftingatk1\n     */\n    readonly shiftingatk1: number[]\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class ResMainPlaneLv {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.hp === undefined) { throw new Error() }\n        this.hp = _json_.hp\n        if (_json_.atk === undefined) { throw new Error() }\n        this.atk = _json_.atk\n    }\n\n    /**\n     * id\n     */\n    readonly id: number\n    /**\n     * 血量\n     */\n    readonly hp: number\n    /**\n     * 攻击\n     */\n    readonly atk: number\n\n    resolve(tables:Tables) {\n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class ResPlane {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.star_level === undefined) { throw new Error() }\n        this.starLevel = _json_.star_level\n        if (_json_.name === undefined) { throw new Error() }\n        this.name = _json_.name\n        if (_json_.icon === undefined) { throw new Error() }\n        this.icon = _json_.icon\n        if (_json_.portrait === undefined) { throw new Error() }\n        this.portrait = _json_.portrait\n        if (_json_.description === undefined) { throw new Error() }\n        this.description = _json_.description\n        if (_json_.quality === undefined) { throw new Error() }\n        this.quality = _json_.quality\n        if (_json_.properties === undefined) { throw new Error() }\n        { this.properties = []; for(let _ele0 of _json_.properties) { let _e0; _e0 = new PlaneProperty(_ele0); this.properties.push(_e0);}}\n        if (_json_.effects === undefined) { throw new Error() }\n        { this.effects = []; for(let _ele0 of _json_.effects) { let _e0; _e0 = new PlaneEffect(_ele0); this.effects.push(_e0);}}\n        if (_json_.materials === undefined) { throw new Error() }\n        { this.materials = []; for(let _ele0 of _json_.materials) { let _e0; _e0 = new PlaneMaterial(_ele0); this.materials.push(_e0);}}\n    }\n\n    /**\n     * ID\n     */\n    readonly id: number\n    /**\n     * 战机星级\n     */\n    readonly starLevel: number\n    /**\n     * 名称\n     */\n    readonly name: string\n    /**\n     * 图标\n     */\n    readonly icon: string\n    /**\n     * 立绘\n     */\n    readonly portrait: string\n    /**\n     * 描述\n     */\n    readonly description: string\n    /**\n     * 品质\n     */\n    readonly quality: QualityType\n    /**\n     * 属性列表\n     */\n    readonly properties: PlaneProperty[]\n    /**\n     * 效果列表\n     */\n    readonly effects: PlaneEffect[]\n    /**\n     * 升星材料\n     */\n    readonly materials: PlaneMaterial[]\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n        for (let _e of this.properties) { _e?.resolve(tables); }\n        for (let _e of this.effects) { _e?.resolve(tables); }\n        for (let _e of this.materials) { _e?.resolve(tables); }\n    }\n}\n\n\n\n\n\nexport class ResSkill {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.name === undefined) { throw new Error() }\n        this.name = _json_.name\n        if (_json_.desc === undefined) { throw new Error() }\n        this.desc = _json_.desc\n        if (_json_.icon === undefined) { throw new Error() }\n        this.icon = _json_.icon\n        if (_json_.cd === undefined) { throw new Error() }\n        this.cd = _json_.cd\n        if (_json_.CostID === undefined) { throw new Error() }\n        this.CostID = _json_.CostID\n        if (_json_.CostNum === undefined) { throw new Error() }\n        this.CostNum = _json_.CostNum\n        if (_json_.conditionID === undefined) { throw new Error() }\n        this.conditionID = _json_.conditionID\n        if (_json_.ApplyBuffs === undefined) { throw new Error() }\n        { this.ApplyBuffs = []; for(let _ele0 of _json_.ApplyBuffs) { let _e0; _e0 = new ApplyBuff(_ele0); this.ApplyBuffs.push(_e0);}}\n    }\n\n    /**\n     * ID\n     */\n    readonly id: number\n    /**\n     * 技能名称\n     */\n    readonly name: string\n    /**\n     * 描述\n     */\n    readonly desc: string\n    /**\n     * 技能图标prefab\n     */\n    readonly icon: string\n    /**\n     * 冷却时间\n     */\n    readonly cd: number\n    /**\n     * 费用ID\n     */\n    readonly CostID: number\n    /**\n     * 费用消耗值\n     */\n    readonly CostNum: number\n    /**\n     * 条件\n     */\n    readonly conditionID: number\n    readonly ApplyBuffs: ApplyBuff[]\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class ResStage {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.mainStage === undefined) { throw new Error() }\n        this.mainStage = _json_.mainStage\n        if (_json_.subStage === undefined) { throw new Error() }\n        this.subStage = _json_.subStage\n        if (_json_.type === undefined) { throw new Error() }\n        this.type = _json_.type\n        if (_json_.enemyGroupID === undefined) { throw new Error() }\n        this.enemyGroupID = _json_.enemyGroupID\n        if (_json_.delay === undefined) { throw new Error() }\n        this.delay = _json_.delay\n        if (_json_.enemyNorRate === undefined) { throw new Error() }\n        this.enemyNorRate = _json_.enemyNorRate\n    }\n\n    /**\n     * id\n     */\n    readonly id: number\n    /**\n     * 关卡\n     */\n    readonly mainStage: number\n    /**\n     * 阶段\n     */\n    readonly subStage: number\n    /**\n     * 类型0:普通敌机 100：boss\n     */\n    readonly type: number\n    /**\n     * 波次id\n     */\n    readonly enemyGroupID: string\n    /**\n     * 延迟时间\n     */\n    readonly delay: number\n    /**\n     * 属性倍率（血量，攻击，碰撞攻击）\n     */\n    readonly enemyNorRate: string\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class ResTask {\n\n    constructor(_json_: any) {\n        if (_json_.task_id === undefined) { throw new Error() }\n        this.taskId = _json_.task_id\n        if (_json_.group_id === undefined) { throw new Error() }\n        this.groupId = _json_.group_id\n        if (_json_.task_class === undefined) { throw new Error() }\n        this.taskClass = _json_.task_class\n        if (_json_.prev_id === undefined) { throw new Error() }\n        this.prevId = _json_.prev_id\n        if (_json_.period_type === undefined) { throw new Error() }\n        this.periodType = _json_.period_type\n        if (_json_.open_type === undefined) { throw new Error() }\n        this.openType = _json_.open_type\n        if (_json_.open_value === undefined) { throw new Error() }\n        this.openValue = _json_.open_value\n        if (_json_.goal_type === undefined) { throw new Error() }\n        this.goalType = _json_.goal_type\n        if (_json_.goal_params === undefined) { throw new Error() }\n        { this.goalParams = []; for(let _ele0 of _json_.goal_params) { let _e0; _e0 = _ele0; this.goalParams.push(_e0);}}\n        if (_json_.accumulate === undefined) { throw new Error() }\n        this.accumulate = _json_.accumulate\n        if (_json_.reward_id === undefined) { throw new Error() }\n        this.rewardId = _json_.reward_id\n        if (_json_.orbit_id === undefined) { throw new Error() }\n        this.orbitId = _json_.orbit_id\n        if (_json_.orbit_value === undefined) { throw new Error() }\n        this.orbitValue = _json_.orbit_value\n        if (_json_.open_date === undefined) { throw new Error() }\n        this.openDate = _json_.open_date\n        if (_json_.open_time === undefined) { throw new Error() }\n        this.openTime = _json_.open_time\n        if (_json_.close_date === undefined) { throw new Error() }\n        this.closeDate = _json_.close_date\n        if (_json_.close_time === undefined) { throw new Error() }\n        this.closeTime = _json_.close_time\n    }\n\n    /**\n     * 任务 ID\n     */\n    readonly taskId: number\n    /**\n     * 任务集 ID\n     */\n    readonly groupId: number\n    /**\n     * 任务类型\n     */\n    readonly taskClass: TaskClass\n    /**\n     * 前置任务 ID\n     */\n    readonly prevId: number\n    /**\n     * 重置周期\n     */\n    readonly periodType: TaskPeriodType\n    /**\n     * 任务接取条件\n     */\n    readonly openType: TaskCondType\n    /**\n     * 条件参数\n     */\n    readonly openValue: number\n    /**\n     * 目标类型\n     */\n    readonly goalType: TaskObjectType\n    /**\n     * 目标参数1 \n     */\n    readonly goalParams: number[]\n    /**\n     * 是否累积\n     */\n    readonly accumulate: boolean\n    /**\n     * 奖励ID\n     */\n    readonly rewardId: number\n    /**\n     * 奖励轨道集ID\n     */\n    readonly orbitId: number\n    /**\n     * 完成奖励值\n     */\n    readonly orbitValue: number\n    /**\n     * 开放日期(yyyymmdd)\n     */\n    readonly openDate: string\n    /**\n     * 开放时间(HHMMSS)\n     */\n    readonly openTime: string\n    /**\n     * 结束日期(yyyymmdd)\n     */\n    readonly closeDate: string\n    /**\n     * 结束时间(HHMMSS)\n     */\n    readonly closeTime: string\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class ResTrack {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.tpe === undefined) { throw new Error() }\n        this.tpe = _json_.tpe\n        if (_json_.value === undefined) { throw new Error() }\n        this.value = _json_.value\n    }\n\n    /**\n     * id\n     */\n    readonly id: number\n    /**\n     * 类型\n     */\n    readonly tpe: number\n    /**\n     * 值(不同的轨迹类型，数据代表的信息不一样)\n     */\n    readonly value: string\n\n    resolve(tables:Tables) {\n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class ResWave {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.enemyGroupID === undefined) { throw new Error() }\n        this.enemyGroupID = _json_.enemyGroupID\n        if (_json_.delay === undefined) { throw new Error() }\n        this.delay = _json_.delay\n        if (_json_.planeType === undefined) { throw new Error() }\n        this.planeType = _json_.planeType\n        if (_json_.planeId === undefined) { throw new Error() }\n        this.planeId = _json_.planeId\n        if (_json_.interval === undefined) { throw new Error() }\n        this.interval = _json_.interval\n        if (_json_.offsetPos === undefined) { throw new Error() }\n        this.offsetPos = _json_.offsetPos\n        if (_json_.num === undefined) { throw new Error() }\n        this.num = _json_.num\n        if (_json_.pos === undefined) { throw new Error() }\n        this.pos = _json_.pos\n        if (_json_.track === undefined) { throw new Error() }\n        this.track = _json_.track\n        if (_json_.trackParams === undefined) { throw new Error() }\n        this.trackParams = _json_.trackParams\n        if (_json_.rotatioSpeed === undefined) { throw new Error() }\n        this.rotatioSpeed = _json_.rotatioSpeed\n        if (_json_.FirstShootDelay === undefined) { throw new Error() }\n        this.FirstShootDelay = _json_.FirstShootDelay\n    }\n\n    /**\n     * id\n     */\n    readonly id: number\n    /**\n     * 波次 ID\n     */\n    readonly enemyGroupID: number\n    /**\n     * 延迟时间\n     */\n    readonly delay: number\n    /**\n     * 0 表示普通敌机\n     */\n    readonly planeType: number\n    /**\n     * 敌机id\n     */\n    readonly planeId: number\n    /**\n     * 生成间隔时间\n     */\n    readonly interval: number\n    /**\n     * 根据敌机数量设置偏移位置\n     */\n    readonly offsetPos: string\n    /**\n     * 生成的敌机数量\n     */\n    readonly num: number\n    /**\n     * 初始位置\n     */\n    readonly pos: string\n    /**\n     * 轨迹路径(次数,轨迹索引,;id,速度,间隔；)\n     */\n    readonly track: string\n    /**\n     * 轨迹参数\n     */\n    readonly trackParams: string\n    /**\n     * 旋转速度\n     */\n    readonly rotatioSpeed: number\n    /**\n     * 首次射击延迟\n     */\n    readonly FirstShootDelay: string\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class SkillCondition {\n\n    constructor(_json_: any) {\n        if (_json_.type === undefined) { throw new Error() }\n        this.type = _json_.type\n        if (_json_.param === undefined) { throw new Error() }\n        { this.param = []; for(let _ele0 of _json_.param) { let _e0; _e0 = _ele0; this.param.push(_e0);}}\n    }\n\n    readonly type: SkillConditionType\n    readonly param: number[]\n\n    resolve(tables:Tables) {\n        \n        \n    }\n}\n\n\n\n\n\n\nexport class TbGlobalAttr {\n\n    private _data: ResGlobalAttr\n    constructor(_json_: any) {\n        if (_json_.length != 1) throw new Error('table mode=one, but size != 1')\n        this._data = new ResGlobalAttr(_json_[0])\n    }\n\n    getData(): ResGlobalAttr { return this._data; }\n\n    /**\n     * 每回合发放的金币\n     */\n    get  GoldProducion(): number { return this._data.GoldProducion; }\n    /**\n     * 体力上限值\n     */\n    get  MaxEnergy(): number { return this._data.MaxEnergy; }\n    /**\n     * 体力恢复的间隔时间\n     */\n    get  EnergyRecoverInterval(): number { return this._data.EnergyRecoverInterval; }\n    /**\n     * 体力恢复的值\n     */\n    get  EnergyRecoverValue(): number { return this._data.EnergyRecoverValue; }\n    /**\n     * 局内道具拾取距离\n     */\n    get  ItemPickUpRadius(): number { return this._data.ItemPickUpRadius; }\n    /**\n     * 受击保护\n     */\n    get  PostHitProtection(): number { return this._data.PostHitProtection; }\n\n    resolve(tables:Tables)\n    {\n        this._data.resolve(tables)\n    }\n    \n}\n\n\n\n\nexport class TbEquipUpgrade {\n    private _dataList: ResEquipUpgrade[]\n    \n    constructor(_json_: any) {\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResEquipUpgrade\n            _v = new ResEquipUpgrade(_json2_)\n            this._dataList.push(_v)\n        }\n    }\n\n    getDataList(): ResEquipUpgrade[] { return this._dataList }\n\n    get(index: number): ResEquipUpgrade | undefined { return this._dataList[index] }\n    \n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbGM {\n    private _dataList: GM[]\n    \n    constructor(_json_: any) {\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: GM\n            _v = new GM(_json2_)\n            this._dataList.push(_v)\n        }\n    }\n\n    getDataList(): GM[] { return this._dataList }\n\n    get(index: number): GM | undefined { return this._dataList[index] }\n    \n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbPlane {\n    private _dataList: ResPlane[]\n    \n    constructor(_json_: any) {\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResPlane\n            _v = new ResPlane(_json2_)\n            this._dataList.push(_v)\n        }\n    }\n\n    getDataList(): ResPlane[] { return this._dataList }\n\n    get(index: number): ResPlane | undefined { return this._dataList[index] }\n    \n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbResBoss {\n    private _dataMap: Map<number, ResBoss>\n    private _dataList: ResBoss[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResBoss>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResBoss\n            _v = new ResBoss(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResBoss> { return this._dataMap; }\n    getDataList(): ResBoss[] { return this._dataList; }\n\n    get(key: number): ResBoss | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbResBuffer {\n    private _dataMap: Map<number, ResBuffer>\n    private _dataList: ResBuffer[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResBuffer>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResBuffer\n            _v = new ResBuffer(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResBuffer> { return this._dataMap; }\n    getDataList(): ResBuffer[] { return this._dataList; }\n\n    get(key: number): ResBuffer | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbResBullet {\n    private _dataMap: Map<number, ResBullet>\n    private _dataList: ResBullet[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResBullet>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResBullet\n            _v = new ResBullet(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResBullet> { return this._dataMap; }\n    getDataList(): ResBullet[] { return this._dataList; }\n\n    get(key: number): ResBullet | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbResChapter {\n    private _dataMap: Map<number, ResChapter>\n    private _dataList: ResChapter[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResChapter>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResChapter\n            _v = new ResChapter(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResChapter> { return this._dataMap; }\n    getDataList(): ResChapter[] { return this._dataList; }\n\n    get(key: number): ResChapter | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbResEffect {\n    private _dataMap: Map<number, ResEffect>\n    private _dataList: ResEffect[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResEffect>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResEffect\n            _v = new ResEffect(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResEffect> { return this._dataMap; }\n    getDataList(): ResEffect[] { return this._dataList; }\n\n    get(key: number): ResEffect | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbResEnemy {\n    private _dataMap: Map<number, ResEnemy>\n    private _dataList: ResEnemy[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResEnemy>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResEnemy\n            _v = new ResEnemy(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResEnemy> { return this._dataMap; }\n    getDataList(): ResEnemy[] { return this._dataList; }\n\n    get(key: number): ResEnemy | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbResEquip {\n    private _dataMap: Map<number, ResEquip>\n    private _dataList: ResEquip[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResEquip>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResEquip\n            _v = new ResEquip(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResEquip> { return this._dataMap; }\n    getDataList(): ResEquip[] { return this._dataList; }\n\n    get(key: number): ResEquip | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbResGameMode {\n    private _dataMap: Map<number, ResGameMode>\n    private _dataList: ResGameMode[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResGameMode>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResGameMode\n            _v = new ResGameMode(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.ID, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResGameMode> { return this._dataMap; }\n    getDataList(): ResGameMode[] { return this._dataList; }\n\n    get(key: number): ResGameMode | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbResItem {\n    private _dataMap: Map<number, ResItem>\n    private _dataList: ResItem[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResItem>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResItem\n            _v = new ResItem(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResItem> { return this._dataMap; }\n    getDataList(): ResItem[] { return this._dataList; }\n\n    get(key: number): ResItem | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbResLevel {\n    private _dataMap: Map<number, ResLevel>\n    private _dataList: ResLevel[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResLevel>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResLevel\n            _v = new ResLevel(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResLevel> { return this._dataMap; }\n    getDataList(): ResLevel[] { return this._dataList; }\n\n    get(key: number): ResLevel | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbResLevelGroup {\n    private _dataMap: Map<number, ResLevelGroup>\n    private _dataList: ResLevelGroup[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResLevelGroup>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResLevelGroup\n            _v = new ResLevelGroup(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResLevelGroup> { return this._dataMap; }\n    getDataList(): ResLevelGroup[] { return this._dataList; }\n\n    get(key: number): ResLevelGroup | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbResMainPlane {\n    private _dataMap: Map<number, ResMainPlane>\n    private _dataList: ResMainPlane[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResMainPlane>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResMainPlane\n            _v = new ResMainPlane(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResMainPlane> { return this._dataMap; }\n    getDataList(): ResMainPlane[] { return this._dataList; }\n\n    get(key: number): ResMainPlane | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbResMainPlaneLv {\n    private _dataMap: Map<number, ResMainPlaneLv>\n    private _dataList: ResMainPlaneLv[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResMainPlaneLv>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResMainPlaneLv\n            _v = new ResMainPlaneLv(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResMainPlaneLv> { return this._dataMap; }\n    getDataList(): ResMainPlaneLv[] { return this._dataList; }\n\n    get(key: number): ResMainPlaneLv | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbResSkill {\n    private _dataMap: Map<number, ResSkill>\n    private _dataList: ResSkill[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResSkill>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResSkill\n            _v = new ResSkill(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResSkill> { return this._dataMap; }\n    getDataList(): ResSkill[] { return this._dataList; }\n\n    get(key: number): ResSkill | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbResStage {\n    private _dataMap: Map<number, ResStage>\n    private _dataList: ResStage[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResStage>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResStage\n            _v = new ResStage(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResStage> { return this._dataMap; }\n    getDataList(): ResStage[] { return this._dataList; }\n\n    get(key: number): ResStage | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbResTask {\n    private _dataMap: Map<number, ResTask>\n    private _dataList: ResTask[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResTask>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResTask\n            _v = new ResTask(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.taskId, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResTask> { return this._dataMap; }\n    getDataList(): ResTask[] { return this._dataList; }\n\n    get(key: number): ResTask | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbResTrack {\n    private _dataMap: Map<number, ResTrack>\n    private _dataList: ResTrack[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResTrack>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResTrack\n            _v = new ResTrack(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResTrack> { return this._dataMap; }\n    getDataList(): ResTrack[] { return this._dataList; }\n\n    get(key: number): ResTrack | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbResWave {\n    private _dataMap: Map<number, ResWave>\n    private _dataList: ResWave[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResWave>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResWave\n            _v = new ResWave(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResWave> { return this._dataMap; }\n    getDataList(): ResWave[] { return this._dataList; }\n\n    get(key: number): ResWave | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\ntype JsonLoader = (file: string) => any\n\nexport class Tables {\n    private _TbGlobalAttr: TbGlobalAttr\n    get TbGlobalAttr(): TbGlobalAttr  { return this._TbGlobalAttr;}\n    private _TbEquipUpgrade: TbEquipUpgrade\n    get TbEquipUpgrade(): TbEquipUpgrade  { return this._TbEquipUpgrade;}\n    private _TbGM: TbGM\n    get TbGM(): TbGM  { return this._TbGM;}\n    private _TbPlane: TbPlane\n    get TbPlane(): TbPlane  { return this._TbPlane;}\n    private _TbResBoss: TbResBoss\n    get TbResBoss(): TbResBoss  { return this._TbResBoss;}\n    private _TbResBuffer: TbResBuffer\n    get TbResBuffer(): TbResBuffer  { return this._TbResBuffer;}\n    private _TbResBullet: TbResBullet\n    get TbResBullet(): TbResBullet  { return this._TbResBullet;}\n    private _TbResChapter: TbResChapter\n    get TbResChapter(): TbResChapter  { return this._TbResChapter;}\n    private _TbResEffect: TbResEffect\n    get TbResEffect(): TbResEffect  { return this._TbResEffect;}\n    private _TbResEnemy: TbResEnemy\n    get TbResEnemy(): TbResEnemy  { return this._TbResEnemy;}\n    private _TbResEquip: TbResEquip\n    get TbResEquip(): TbResEquip  { return this._TbResEquip;}\n    private _TbResGameMode: TbResGameMode\n    get TbResGameMode(): TbResGameMode  { return this._TbResGameMode;}\n    private _TbResItem: TbResItem\n    get TbResItem(): TbResItem  { return this._TbResItem;}\n    private _TbResLevel: TbResLevel\n    get TbResLevel(): TbResLevel  { return this._TbResLevel;}\n    private _TbResLevelGroup: TbResLevelGroup\n    get TbResLevelGroup(): TbResLevelGroup  { return this._TbResLevelGroup;}\n    private _TbResMainPlane: TbResMainPlane\n    get TbResMainPlane(): TbResMainPlane  { return this._TbResMainPlane;}\n    private _TbResMainPlaneLv: TbResMainPlaneLv\n    get TbResMainPlaneLv(): TbResMainPlaneLv  { return this._TbResMainPlaneLv;}\n    private _TbResSkill: TbResSkill\n    get TbResSkill(): TbResSkill  { return this._TbResSkill;}\n    private _TbResStage: TbResStage\n    get TbResStage(): TbResStage  { return this._TbResStage;}\n    private _TbResTask: TbResTask\n    get TbResTask(): TbResTask  { return this._TbResTask;}\n    private _TbResTrack: TbResTrack\n    get TbResTrack(): TbResTrack  { return this._TbResTrack;}\n    private _TbResWave: TbResWave\n    get TbResWave(): TbResWave  { return this._TbResWave;}\n\n    constructor(loader: JsonLoader) {\n        this._TbGlobalAttr = new TbGlobalAttr(loader('tbglobalattr'))\n        this._TbEquipUpgrade = new TbEquipUpgrade(loader('tbequipupgrade'))\n        this._TbGM = new TbGM(loader('tbgm'))\n        this._TbPlane = new TbPlane(loader('tbplane'))\n        this._TbResBoss = new TbResBoss(loader('tbresboss'))\n        this._TbResBuffer = new TbResBuffer(loader('tbresbuffer'))\n        this._TbResBullet = new TbResBullet(loader('tbresbullet'))\n        this._TbResChapter = new TbResChapter(loader('tbreschapter'))\n        this._TbResEffect = new TbResEffect(loader('tbreseffect'))\n        this._TbResEnemy = new TbResEnemy(loader('tbresenemy'))\n        this._TbResEquip = new TbResEquip(loader('tbresequip'))\n        this._TbResGameMode = new TbResGameMode(loader('tbresgamemode'))\n        this._TbResItem = new TbResItem(loader('tbresitem'))\n        this._TbResLevel = new TbResLevel(loader('tbreslevel'))\n        this._TbResLevelGroup = new TbResLevelGroup(loader('tbreslevelgroup'))\n        this._TbResMainPlane = new TbResMainPlane(loader('tbresmainplane'))\n        this._TbResMainPlaneLv = new TbResMainPlaneLv(loader('tbresmainplanelv'))\n        this._TbResSkill = new TbResSkill(loader('tbresskill'))\n        this._TbResStage = new TbResStage(loader('tbresstage'))\n        this._TbResTask = new TbResTask(loader('tbrestask'))\n        this._TbResTrack = new TbResTrack(loader('tbrestrack'))\n        this._TbResWave = new TbResWave(loader('tbreswave'))\n\n        this._TbGlobalAttr.resolve(this)\n        this._TbEquipUpgrade.resolve(this)\n        this._TbGM.resolve(this)\n        this._TbPlane.resolve(this)\n        this._TbResBoss.resolve(this)\n        this._TbResBuffer.resolve(this)\n        this._TbResBullet.resolve(this)\n        this._TbResChapter.resolve(this)\n        this._TbResEffect.resolve(this)\n        this._TbResEnemy.resolve(this)\n        this._TbResEquip.resolve(this)\n        this._TbResGameMode.resolve(this)\n        this._TbResItem.resolve(this)\n        this._TbResLevel.resolve(this)\n        this._TbResLevelGroup.resolve(this)\n        this._TbResMainPlane.resolve(this)\n        this._TbResMainPlaneLv.resolve(this)\n        this._TbResSkill.resolve(this)\n        this._TbResStage.resolve(this)\n        this._TbResTask.resolve(this)\n        this._TbResTrack.resolve(this)\n        this._TbResWave.resolve(this)\n    }\n}\n\n"]}