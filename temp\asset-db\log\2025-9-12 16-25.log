2025-9-12 16:25:52-log: Cannot access game frame or container.
2025-9-12 16:25:52-debug: asset-db:require-engine-code (413ms)
2025-9-12 16:25:52-log: meshopt wasm decoder initialized
2025-9-12 16:25:52-log: [bullet]:bullet wasm lib loaded.
2025-9-12 16:25:52-log: [box2d]:box2d wasm lib loaded.
2025-9-12 16:25:52-log: Cocos Creator v3.8.6
2025-9-12 16:25:52-log: Using legacy pipeline
2025-9-12 16:25:52-log: Forward render pipeline initialized.
2025-9-12 16:25:52-debug: [Assets Memory track]: asset-db-plugin-register: programming start:80.92MB, end 83.93MB, increase: 3.01MB
2025-9-12 16:25:52-debug: [Assets Memory track]: asset-db:worker-init: initEngine start:30.80MB, end 80.01MB, increase: 49.21MB
2025-9-12 16:25:53-debug: [Assets Memory track]: asset-db-plugin-register: project start:80.77MB, end 228.42MB, increase: 147.65MB
2025-9-12 16:25:53-debug: [Assets Memory track]: asset-db-plugin-register: builder start:83.96MB, end 224.80MB, increase: 140.83MB
2025-9-12 16:25:53-debug: [Assets Memory track]: asset-db-plugin-register: engine-extends start:225.04MB, end 228.21MB, increase: 3.17MB
2025-9-12 16:25:53-debug: [Assets Memory track]: asset-db:worker-init: initPlugin start:80.04MB, end 228.45MB, increase: 148.41MB
2025-9-12 16:25:53-debug: run package(google-play) handler(enable) start
2025-9-12 16:25:53-debug: run package(google-play) handler(enable) success!
2025-9-12 16:25:53-debug: run package(harmonyos-next) handler(enable) start
2025-9-12 16:25:53-debug: run package(harmonyos-next) handler(enable) success!
2025-9-12 16:25:53-debug: run package(huawei-agc) handler(enable) start
2025-9-12 16:25:53-debug: run package(honor-mini-game) handler(enable) success!
2025-9-12 16:25:53-debug: run package(huawei-agc) handler(enable) success!
2025-9-12 16:25:53-debug: run package(honor-mini-game) handler(enable) start
2025-9-12 16:25:53-debug: run package(huawei-quick-game) handler(enable) start
2025-9-12 16:25:53-debug: run package(huawei-quick-game) handler(enable) success!
2025-9-12 16:25:53-debug: run package(ios) handler(enable) success!
2025-9-12 16:25:53-debug: run package(ios) handler(enable) start
2025-9-12 16:25:53-debug: run package(linux) handler(enable) success!
2025-9-12 16:25:53-debug: run package(mac) handler(enable) start
2025-9-12 16:25:53-debug: run package(linux) handler(enable) start
2025-9-12 16:25:53-debug: run package(mac) handler(enable) success!
2025-9-12 16:25:53-debug: run package(migu-mini-game) handler(enable) start
2025-9-12 16:25:53-debug: run package(ohos) handler(enable) start
2025-9-12 16:25:53-debug: run package(native) handler(enable) start
2025-9-12 16:25:53-debug: run package(ohos) handler(enable) success!
2025-9-12 16:25:53-debug: run package(native) handler(enable) success!
2025-9-12 16:25:53-debug: run package(oppo-mini-game) handler(enable) start
2025-9-12 16:25:53-debug: run package(oppo-mini-game) handler(enable) success!
2025-9-12 16:25:53-debug: run package(runtime-dev-tools) handler(enable) start
2025-9-12 16:25:53-debug: run package(migu-mini-game) handler(enable) success!
2025-9-12 16:25:53-debug: run package(taobao-mini-game) handler(enable) success!
2025-9-12 16:25:53-debug: run package(runtime-dev-tools) handler(enable) success!
2025-9-12 16:25:53-debug: run package(taobao-mini-game) handler(enable) start
2025-9-12 16:25:53-debug: run package(web-desktop) handler(enable) start
2025-9-12 16:25:53-debug: run package(vivo-mini-game) handler(enable) success!
2025-9-12 16:25:53-debug: run package(vivo-mini-game) handler(enable) start
2025-9-12 16:25:53-debug: run package(web-mobile) handler(enable) success!
2025-9-12 16:25:53-debug: run package(web-desktop) handler(enable) success!
2025-9-12 16:25:53-debug: run package(web-mobile) handler(enable) start
2025-9-12 16:25:53-debug: run package(wechatgame) handler(enable) success!
2025-9-12 16:25:53-debug: run package(wechatprogram) handler(enable) success!
2025-9-12 16:25:53-debug: run package(wechatgame) handler(enable) start
2025-9-12 16:25:53-debug: run package(xiaomi-quick-game) handler(enable) start
2025-9-12 16:25:53-debug: run package(wechatprogram) handler(enable) start
2025-9-12 16:25:53-debug: run package(xiaomi-quick-game) handler(enable) success!
2025-9-12 16:25:53-debug: run package(windows) handler(enable) start
2025-9-12 16:25:53-debug: run package(windows) handler(enable) success!
2025-9-12 16:25:53-debug: run package(cocos-service) handler(enable) success!
2025-9-12 16:25:53-debug: run package(cocos-service) handler(enable) start
2025-9-12 16:25:53-debug: run package(im-plugin) handler(enable) start
2025-9-12 16:25:53-debug: run package(fix-asset-default-userdata) handler(enable) success!
2025-9-12 16:25:53-debug: asset-db:worker-init: initPlugin (981ms)
2025-9-12 16:25:53-debug: run package(fix-asset-default-userdata) handler(enable) start
2025-9-12 16:25:53-debug: run package(im-plugin) handler(enable) success!
2025-9-12 16:25:53-debug: [Assets Memory track]: asset-db:worker-init start:30.80MB, end 225.27MB, increase: 194.48MB
2025-9-12 16:25:53-debug: Run asset db hook programming:beforePreStart ...
2025-9-12 16:25:53-debug: Run asset db hook engine-extends:beforePreStart ...
2025-9-12 16:25:53-debug: Run asset db hook engine-extends:beforePreStart success!
2025-9-12 16:25:53-debug: Run asset db hook programming:beforePreStart success!
2025-9-12 16:25:53-debug: run package(emitter-editor) handler(enable) start
2025-9-12 16:25:53-debug: run package(emitter-editor) handler(enable) success!
2025-9-12 16:25:53-debug: run package(level-editor) handler(enable) start
2025-9-12 16:25:53-debug: run package(level-editor) handler(enable) success!
2025-9-12 16:25:53-debug: asset-db:worker-init (1509ms)
2025-9-12 16:25:53-debug: asset-db-hook-programming-beforePreStart (45ms)
2025-9-12 16:25:53-debug: asset-db-hook-engine-extends-beforePreStart (45ms)
2025-9-12 16:25:53-debug: Preimport db internal success
2025-9-12 16:25:53-debug: Preimport db assets success
2025-9-12 16:25:53-debug: Run asset db hook programming:afterPreStart ...
2025-9-12 16:25:53-debug: starting packer-driver...
2025-9-12 16:25:53-debug: run package(localization-editor) handler(enable) start
2025-9-12 16:25:53-debug: run package(localization-editor) handler(enable) success!
2025-9-12 16:25:53-debug: run package(wave-editor) handler(enable) start
2025-9-12 16:25:53-debug: run package(wave-editor) handler(enable) success!
2025-9-12 16:25:53-debug: run package(placeholder) handler(enable) start
2025-9-12 16:25:53-debug: run package(placeholder) handler(enable) success!
2025-9-12 16:25:58-debug: initialize scripting environment...
2025-9-12 16:25:58-debug: [[Executor]] prepare before lock
2025-9-12 16:25:58-debug: Set detail map pack:///resolution-detail-map.json: {}
2025-9-12 16:25:58-debug: [[Executor]] prepare after unlock
2025-9-12 16:25:58-debug: Run asset db hook programming:afterPreStart success!
2025-9-12 16:25:58-debug: Run asset db hook engine-extends:afterPreStart success!
2025-9-12 16:25:58-debug: Run asset db hook engine-extends:afterPreStart ...
2025-9-12 16:25:58-debug: [Assets Memory track]: asset-db:worker-init: preStart start:225.29MB, end 229.35MB, increase: 4.07MB
2025-9-12 16:25:58-debug: Start up the 'internal' database...
2025-9-12 16:25:58-debug: asset-db-hook-programming-afterPreStart (5036ms)
2025-9-12 16:25:58-debug: asset-db-hook-engine-extends-afterPreStart (186ms)
2025-9-12 16:25:58-debug: asset-db:worker-effect-data-processing (186ms)
2025-9-12 16:25:58-debug: Start up the 'assets' database...
2025-9-12 16:25:58-debug: asset-db:worker-startup-database[internal] (5253ms)
2025-9-12 16:25:58-debug: lazy register asset handler *
2025-9-12 16:25:58-debug: lazy register asset handler text
2025-9-12 16:25:58-debug: lazy register asset handler directory
2025-9-12 16:25:58-debug: lazy register asset handler spine-data
2025-9-12 16:25:58-debug: lazy register asset handler dragonbones-atlas
2025-9-12 16:25:58-debug: lazy register asset handler json
2025-9-12 16:25:58-debug: lazy register asset handler javascript
2025-9-12 16:25:58-debug: lazy register asset handler terrain
2025-9-12 16:25:58-debug: lazy register asset handler dragonbones
2025-9-12 16:25:58-debug: lazy register asset handler prefab
2025-9-12 16:25:58-debug: lazy register asset handler typescript
2025-9-12 16:25:58-debug: lazy register asset handler sprite-frame
2025-9-12 16:25:58-debug: lazy register asset handler tiled-map
2025-9-12 16:25:58-debug: lazy register asset handler scene
2025-9-12 16:25:58-debug: lazy register asset handler buffer
2025-9-12 16:25:58-debug: lazy register asset handler image
2025-9-12 16:25:58-debug: lazy register asset handler sign-image
2025-9-12 16:25:58-debug: lazy register asset handler texture-cube
2025-9-12 16:25:58-debug: lazy register asset handler texture
2025-9-12 16:25:58-debug: lazy register asset handler alpha-image
2025-9-12 16:25:58-debug: lazy register asset handler erp-texture-cube
2025-9-12 16:25:58-debug: lazy register asset handler render-texture
2025-9-12 16:25:58-debug: lazy register asset handler gltf
2025-9-12 16:25:58-debug: lazy register asset handler gltf-mesh
2025-9-12 16:25:58-debug: lazy register asset handler texture-cube-face
2025-9-12 16:25:58-debug: lazy register asset handler rt-sprite-frame
2025-9-12 16:25:58-debug: lazy register asset handler gltf-animation
2025-9-12 16:25:58-debug: lazy register asset handler gltf-scene
2025-9-12 16:25:58-debug: lazy register asset handler gltf-material
2025-9-12 16:25:58-debug: lazy register asset handler gltf-skeleton
2025-9-12 16:25:58-debug: lazy register asset handler material
2025-9-12 16:25:58-debug: lazy register asset handler gltf-embeded-image
2025-9-12 16:25:58-debug: lazy register asset handler fbx
2025-9-12 16:25:58-debug: lazy register asset handler effect
2025-9-12 16:25:58-debug: lazy register asset handler physics-material
2025-9-12 16:25:58-debug: lazy register asset handler effect-header
2025-9-12 16:25:58-debug: lazy register asset handler animation-clip
2025-9-12 16:25:58-debug: lazy register asset handler animation-graph-variant
2025-9-12 16:25:58-debug: lazy register asset handler animation-mask
2025-9-12 16:25:58-debug: lazy register asset handler animation-graph
2025-9-12 16:25:58-debug: lazy register asset handler audio-clip
2025-9-12 16:25:58-debug: lazy register asset handler ttf-font
2025-9-12 16:25:58-debug: lazy register asset handler bitmap-font
2025-9-12 16:25:58-debug: lazy register asset handler sprite-atlas
2025-9-12 16:25:58-debug: lazy register asset handler particle
2025-9-12 16:25:58-debug: lazy register asset handler auto-atlas
2025-9-12 16:25:58-debug: lazy register asset handler render-pipeline
2025-9-12 16:25:58-debug: lazy register asset handler render-stage
2025-9-12 16:25:58-debug: lazy register asset handler label-atlas
2025-9-12 16:25:58-debug: lazy register asset handler instantiation-material
2025-9-12 16:25:58-debug: lazy register asset handler render-flow
2025-9-12 16:25:58-debug: lazy register asset handler instantiation-animation
2025-9-12 16:25:58-debug: lazy register asset handler instantiation-skeleton
2025-9-12 16:25:58-debug: lazy register asset handler instantiation-mesh
2025-9-12 16:25:58-debug: lazy register asset handler video-clip
2025-9-12 16:25:58-debug: asset-db:worker-startup-database[assets] (5264ms)
2025-9-12 16:25:58-debug: asset-db:start-database (5333ms)
2025-9-12 16:25:58-debug: asset-db:ready (8324ms)
2025-9-12 16:25:58-debug: fix the bug of updateDefaultUserData
2025-9-12 16:25:58-debug: init worker message success
2025-9-12 16:25:58-debug: [Build Memory track]: builder:worker-init start:194.81MB, end 207.24MB, increase: 12.42MB
2025-9-12 16:25:58-debug: builder:worker-init (267ms)
2025-9-12 16:30:23-debug: refresh db internal success
2025-9-12 16:30:23-debug: refresh db assets success
2025-9-12 16:30:23-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-12 16:30:23-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-12 16:30:23-debug: asset-db:refresh-all-database (172ms)
2025-9-12 16:30:23-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
