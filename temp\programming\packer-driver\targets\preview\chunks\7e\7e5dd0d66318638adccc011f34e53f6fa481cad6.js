System.register(["__unresolved_0", "cc", "cc/env", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, instantiate, input, Input, KeyCode, Component, RichText, Node, Vec3, Graphics, assetManager, EDITOR, Emitter, BulletSystem, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _dec8, _dec9, _class, _class2, _descriptor, _descriptor2, _descriptor3, _class3, _crd, ccclass, playOnFocus, executeInEditMode, property, disallowMultiple, menu, EmitterEditor;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfEmitter(extras) {
    _reporterNs.report("Emitter", "../../scripts/game/bullet/Emitter", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBulletSystem(extras) {
    _reporterNs.report("BulletSystem", "../../scripts/game/bullet/BulletSystem", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      instantiate = _cc.instantiate;
      input = _cc.input;
      Input = _cc.Input;
      KeyCode = _cc.KeyCode;
      Component = _cc.Component;
      RichText = _cc.RichText;
      Node = _cc.Node;
      Vec3 = _cc.Vec3;
      Graphics = _cc.Graphics;
      assetManager = _cc.assetManager;
    }, function (_ccEnv) {
      EDITOR = _ccEnv.EDITOR;
    }, function (_unresolved_2) {
      Emitter = _unresolved_2.Emitter;
    }, function (_unresolved_3) {
      BulletSystem = _unresolved_3.BulletSystem;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "6c1a6Gwu4lMHr2sSELvATS6", "EmitterEditor", undefined);

      __checkObsolete__(['_decorator', 'misc', 'instantiate', 'input', 'Input', 'EventKeyboard', 'KeyCode', 'Component', 'RichText', 'Node', 'Vec3', 'Graphics', 'assetManager']);

      ({
        ccclass,
        playOnFocus,
        executeInEditMode,
        property,
        disallowMultiple,
        menu
      } = _decorator);

      _export("EmitterEditor", EmitterEditor = (_dec = ccclass('EmitterEditor'), _dec2 = menu('子弹系统/发射器编辑器'), _dec3 = playOnFocus(true), _dec4 = executeInEditMode(true), _dec5 = disallowMultiple(true), _dec6 = property({
        visible: false
      }), _dec7 = property({
        displayName: "目标帧率"
      }), _dec8 = property({
        type: RichText,
        override: true,
        displayName: "信息显示"
      }), _dec9 = property({
        type: Node,
        displayName: "玩家节点"
      }), _dec(_class = _dec2(_class = _dec3(_class = _dec4(_class = _dec5(_class = (_class2 = (_class3 = class EmitterEditor extends Component {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "fixedDelta", _descriptor, this);

          _initializerDefineProperty(this, "richText", _descriptor2, this);

          _initializerDefineProperty(this, "playerNode", _descriptor3, this);

          this._updateInEditor = false;
          this._graphicsCom = null;
          this._playerPos = new Vec3(0, 0, 0);
        }

        // Fixed time step (e.g., 60 FPS), 单位: 毫秒
        get targetFrameRate() {
          return 1000 / this.fixedDelta;
        }

        set targetFrameRate(value) {
          this.fixedDelta = 1000 / value;
        }

        get graphics() {
          if (!this._graphicsCom) {
            this._graphicsCom = this.getComponent(Graphics) || this.addComponent(Graphics);
          }

          return this._graphicsCom;
        }

        resetInEditor() {
          this._updateInEditor = true; // console.log('resetInEditor');
        }

        onFocusInEditor() {
          this._updateInEditor = true; // console.log('onFocusInEditor');
          // @ts-ignore
          // Editor.Selection.select('node', BulletSystem.allEmitters.map(emitter => emitter.node.uuid));
          // loop all children to find emitters

          this.node.walk(node => {
            var emitter = node.getComponent(_crd && Emitter === void 0 ? (_reportPossibleCrUseOfEmitter({
              error: Error()
            }), Emitter) : Emitter);

            if (emitter) {
              emitter.setIsActive(true);
            }
          });
        }

        onLostFocusInEditor() {
          this._updateInEditor = false;
          this.reset();
        }

        onLoad() {
          input.on(Input.EventType.KEY_PRESSING, this.onKeyPressing, this);
        }

        onDestroy() {
          input.off(Input.EventType.KEY_PRESSING, this.onKeyPressing, this);
        }

        onKeyPressing(event) {
          console.log('key pressing:', event.keyCode); // if (!this._updateInEditor) return;
          // use wasd to move player sprite(which is not present right now)

          if (this.playerNode) {
            this.playerNode.getPosition(this._playerPos);

            if (event.keyCode === KeyCode.KEY_W) {
              this._playerPos.y += 10;
            }

            if (event.keyCode === KeyCode.KEY_S) {
              this._playerPos.y -= 10;
            }

            if (event.keyCode === KeyCode.KEY_A) {
              this._playerPos.x -= 10;
            }

            if (event.keyCode === KeyCode.KEY_D) {
              this._playerPos.x += 10;
            }

            this.playerNode.setPosition(this._playerPos);
          }
        }

        start() {
          this.reset();
        }

        reset() {
          EmitterEditor.frameCount = 0;
          EmitterEditor.frameTimeInMilliseconds = 0;
          (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
            error: Error()
          }), BulletSystem) : BulletSystem).destroyAllBullets(true);
          this.node.walk(node => {
            var emitter = node.getComponent(_crd && Emitter === void 0 ? (_reportPossibleCrUseOfEmitter({
              error: Error()
            }), Emitter) : Emitter);

            if (emitter) {
              emitter.setIsActive(false);
            }
          });
          (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
            error: Error()
          }), BulletSystem) : BulletSystem).allEventGroups = [];
          this._playerPos.x = this._playerPos.y = this._playerPos.z = 0;

          if (this.playerNode) {
            this.playerNode.setPosition(this._playerPos);
          }

          console.log('reset: ', this._updateInEditor);
        }

        update(dt) {
          if (EDITOR && this._updateInEditor) {
            this.updateInfoText();
            var milli_dt = dt * 1000;
            EmitterEditor.frameCount += 1;
            EmitterEditor.frameTimeInMilliseconds += milli_dt;
            (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
              error: Error()
            }), BulletSystem) : BulletSystem).tick(dt);
          }
        }

        updateInfoText() {
          if (this.richText) {
            this.richText.string = "\u5F53\u524D\u65F6\u95F4: " + EmitterEditor.frameTimeInMilliseconds.toFixed(2) + "\n\u5F53\u524D\u53D1\u5C04\u5668\u6570\u91CF: " + (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
              error: Error()
            }), BulletSystem) : BulletSystem).allEmitters.length + "\n\u5F53\u524D\u5B50\u5F39\u6570\u91CF: " + (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
              error: Error()
            }), BulletSystem) : BulletSystem).allBullets.length + "\n\u5F53\u524D\u4E8B\u4EF6\u7EC4\u6570\u91CF: " + (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
              error: Error()
            }), BulletSystem) : BulletSystem).allEventGroups.length;
          }
        } // 编辑器方法


        instantiatePrefab(prefabUuid) {
          // replace db://assets/resources/game/prefabs/emitter/ with assets/resources/game/prefabs/emitter/
          //prefabUrl = prefabUrl.replace('db://', '');
          assetManager.loadAny({
            uuid: prefabUuid
          }, (err, prefab) => {
            if (err) {
              console.log('Failed to load prefab:', err);
              return;
            }

            var node = instantiate(prefab);
            var parent = this.node;
            parent.addChild(node);
          });
        } // public saveToPrefab(nodeUuid: string, prefabUrl: string): Promise<string> {
        //     console.log('saveToPrefab in Component:', nodeUuid, prefabUrl);        
        //     return new Promise<string>((resolve, reject) => {
        //         const scene = director.getScene();
        //         const target = scene!.getChildByUuid(nodeUuid);
        //         if (!target) {
        //             console.error("node not found:", nodeUuid);
        //             reject();
        //             return;
        //         }
        //         const json = cce.Utils.serialize(target);
        //         // 将节点保存为 Prefab
        //         // _utils.applyTargetOverrides(target as Node);
        //         // Editor.Message.request('asset-db', 'save-asset', prefabUrl, json);
        //         resolve(json);
        //     });
        // }


      }, _class3.frameCount = 0, _class3.frameTimeInMilliseconds = 0, _class3), (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "fixedDelta", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 16.67;
        }
      }), _applyDecoratedDescriptor(_class2.prototype, "targetFrameRate", [_dec7], Object.getOwnPropertyDescriptor(_class2.prototype, "targetFrameRate"), _class2.prototype), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "richText", [_dec8], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "playerNode", [_dec9], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      })), _class2)) || _class) || _class) || _class) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=7e5dd0d66318638adccc011f34e53f6fa481cad6.js.map