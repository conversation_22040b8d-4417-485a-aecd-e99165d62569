'use strict';
Object.defineProperty(exports, "__esModule", { value: true });
exports.$ = exports.template = void 0;
exports.update = update;
exports.ready = ready;
const { updatePropByDump, disconnectGroup } = require('./../../prop');
exports.template = `
<div class="component-container"></div>
<ui-button class="uiButton">保存到Prefab</ui-button>
`;
exports.style = /* css */ `
.uiButton{
    width: 96%;
    margin-left: 2%;
    margin-top: 12px;
    height:25px;
}
`;
exports.$ = {
    componentContainer: '.component-container',
    uiButton: ".uiButton"
};
function setVariantsOnList(arr, listKey, aKey, bKey, isA) {
    var _a, _b, _c, _d, _e;
    if (!arr)
        return;
    const egTpl = (_a = arr.elementTypeData) === null || _a === void 0 ? void 0 : _a.value;
    const listTpl = (_c = (_b = egTpl === null || egTpl === void 0 ? void 0 : egTpl[listKey]) === null || _b === void 0 ? void 0 : _b.elementTypeData) === null || _c === void 0 ? void 0 : _c.value;
    if (listTpl) {
        if (listTpl[aKey])
            listTpl[aKey].visible = isA;
        if (listTpl[bKey])
            listTpl[bKey].visible = !isA;
    }
    const egItems = arr.value;
    if (!Array.isArray(egItems))
        return;
    for (const eg of egItems) {
        const list = (_d = eg === null || eg === void 0 ? void 0 : eg.value) === null || _d === void 0 ? void 0 : _d[listKey];
        const itemTpl = (_e = list === null || list === void 0 ? void 0 : list.elementTypeData) === null || _e === void 0 ? void 0 : _e.value;
        if (itemTpl) {
            if (itemTpl[aKey])
                itemTpl[aKey].visible = isA;
            if (itemTpl[bKey])
                itemTpl[bKey].visible = !isA;
        }
        const items = list === null || list === void 0 ? void 0 : list.value;
        if (!Array.isArray(items))
            continue;
        for (const it of items) {
            const v = it === null || it === void 0 ? void 0 : it.value;
            if (v === null || v === void 0 ? void 0 : v[aKey])
                v[aKey].visible = isA;
            if (v === null || v === void 0 ? void 0 : v[bKey])
                v[bKey].visible = !isA;
        }
    }
}
const setConditionVariant = (arr, kind) => setVariantsOnList(arr, 'conditions', 'emitterConditionType', 'bulletConditionType', kind === 'emitter');
const setActionVariant = (arr, kind) => setVariantsOnList(arr, 'actions', 'emitterActionType', 'bulletActionType', kind === 'emitter');
function update(dump) {
    var _a, _b, _c, _d, _e, _f;
    const egEmitter = (_c = (_b = (_a = dump.value) === null || _a === void 0 ? void 0 : _a.emitterData) === null || _b === void 0 ? void 0 : _b.value) === null || _c === void 0 ? void 0 : _c.eventGroupData;
    if (egEmitter) {
        setConditionVariant(egEmitter, 'emitter');
        setActionVariant(egEmitter, 'emitter');
    }
    const egBullet = (_f = (_e = (_d = dump.value) === null || _d === void 0 ? void 0 : _d.bulletData) === null || _e === void 0 ? void 0 : _e.value) === null || _f === void 0 ? void 0 : _f.eventGroupData;
    if (egBullet) {
        setConditionVariant(egBullet, 'bullet');
        setActionVariant(egBullet, 'bullet');
    }
    updatePropByDump(this, dump);
    this.dump = dump;
}
async function ready() {
    disconnectGroup(this);
    this.$.uiButton.addEventListener('confirm', async () => {
        var _a, _b, _c;
        console.log('save-to-prefab', this.dump);
        const name = (_b = (_a = this.dump.value) === null || _a === void 0 ? void 0 : _a.name.value) === null || _b === void 0 ? void 0 : _b.replace('<Emitter>', '');
        const emitterDir = 'db://assets/resources/game/prefabs/emitter/' + name + '.prefab';
        // @ts-ignore
        const prefabUuid = await Editor.Message.request('asset-db', 'query-uuid', emitterDir);
        // @ts-ignore
        Editor.Message.request('scene', 'execute-scene-script', {
            name: 'emitter-editor',
            method: 'saveToPrefab',
            args: [(_c = this.dump) === null || _c === void 0 ? void 0 : _c.value.uuid, name, emitterDir]
        });
    });
}
//# sourceMappingURL=data:application/json;base64,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