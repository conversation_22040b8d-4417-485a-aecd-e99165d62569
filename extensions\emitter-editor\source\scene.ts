import { join } from 'path';

// 临时在当前模块增加编辑器内的模块为搜索路径，为了能够正常 require 到 cc 模块，后续版本将优化调用方式
// @ts-ignore
module.paths.push(join(Editor.App.path, 'node_modules'));

// 当前版本需要在 module.paths 修改后才能正常使用 cc 模块
// 并且如果希望正常显示 cc 的定义，需要手动将 engine 文件夹里的 cc.d.ts 添加到插件的 tsconfig 里
// 当前版本的 cc 定义文件可以在当前项目的 temp/declarations/cc.d.ts 找到
import { Prefab, Node, director, instantiate, assetManager, Vec2, Vec3 } from 'cc';
const { _utils } = Prefab;

declare const cce: any;

export function load() { 

};

export function unload() { 

};

export const methods = { 
    instantiatePrefab(component_uuid:string, prefabUuid: string) {
        // console.log('instantiatePrefab:', component_uuid, prefabUuid);

        let targetNode = director.getScene()?.getChildByUuid(component_uuid);
        if (!targetNode) {
            targetNode = director.getScene()?.getChildByName('Canvas');
        }

        if (targetNode) {
            // console.log("Canvas node found: ", targetNode.getComponent("EmitterEditor"));
            // @ts-ignore
            Editor.Message.request('scene', 'execute-component-method', {
                uuid: targetNode.getComponent("EmitterEditor")?.uuid,
                name: 'instantiatePrefab',
                args: [prefabUuid]
            });
        }
    },

    async saveToPrefab(component_uuid:string, nodeName: string, prefabUuid: string) {
        // console.log('saveToPrefab:', component_uuid, nodeName, prefabUuid);

        const scene = director.getScene();
        const target = scene!.getChildByPath(`Canvas/${nodeName}`);
        if (!target) {
            console.error("node not found:", nodeName);
            return;
        }

        cce.Prefab.createPrefabAssetFromNode(target.uuid, prefabUuid);
        // const json = cce.Utils.serialize(target);
        // console.log('Prefab JSON:', json);
        
        // Editor.Message.request('asset-db', 'save-asset', prefabUuid, json);
    },

    async createNewEmitter(prefabName: string, prefabPath: string) {
        const scene = director.getScene();
        const target = scene!.getChildByName('Canvas');
        if (!target) {
            console.error("Canvas node not found");
            return;
        }

        let emitterNode = new Node(prefabName);
        emitterNode.parent = target;
        emitterNode.setPosition(new Vec3(0, 0, 0));
        emitterNode.addComponent('cc.UITransform'); // Ensure it has a transform component
        emitterNode.addComponent('Emitter');
        
        const nodeUuid = emitterNode.uuid;
        await cce.Prefab.createPrefabAssetFromNode(nodeUuid, prefabPath);
    },

    movePlayer(direction: Vec3) {
        const scene = director.getScene();
        const target = scene!.getChildByName('Canvas');
        if (!target) {
            console.error("Canvas node not found");
            return;
        }

        const playerNode = target.getChildByName('Player');
        if (!playerNode) {
            console.error("Player node not found");
            return;
        }

        playerNode.setPosition(playerNode.position.add(direction));
    },

    movePlayerUp() {
        console.log('movePlayerUp scene');
        this.movePlayer(new Vec3(0, 10, 0));
    }
};