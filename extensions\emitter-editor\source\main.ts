import { Vec3 } from 'cc';

/**
 * @en Registration method for the main process of Extension
 * @zh 为扩展的主进程的注册方法
 */
export const methods: { [key: string]: (...any: any) => any } = {
    /**
     * @en A method that can be triggered by message
     * @zh 通过 message 触发的方法
     */
    movePlayerUp() {
        console.log('movePlayerUp');
        Editor.Message.request('scene', 'execute-scene-script', {
                name: 'emitter-editor',
                method: 'movePlayer',
                args: [new Vec3(0, 10, 0)]
            });
    },
    movePlayerDown() {
        console.log('movePlayerDown');
        Editor.Message.request('scene', 'execute-scene-script', {
                name: 'emitter-editor',
                method: 'movePlayer',
                args: [new Vec3(0, -10, 0)]
            });
    },
    movePlayerLeft() {
        console.log('movePlayerLeft');
        Editor.Message.request('scene', 'execute-scene-script', {
                name: 'emitter-editor',
                method: 'movePlayer',
                args: [new Vec3(-10, 0, 0)]
            });
    },
    movePlayerRight() {
        console.log('movePlayerRight');
        Editor.Message.request('scene', 'execute-scene-script', {
                name: 'emitter-editor',
                method: 'movePlayer',
                args: [new Vec3(10, 0, 0)]
            });
    },

    testMessage() {
        console.log('Test message received!');
        return 'Test successful';
    }
};

/**
 * @en Method Triggered on Extension Startup
 * @zh 扩展启动时触发的方法
 */
export function load() {
    console.log('emitter-editor extension loaded');
    console.log('Available methods:', Object.keys(methods));
}

/**
 * @en Method triggered when uninstalling the extension
 * @zh 卸载扩展时触发的方法
 */
export function unload() { }
